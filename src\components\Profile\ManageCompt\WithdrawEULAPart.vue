<template>
  <div class="parent" @click="btnWithdraw">
    <el-text>撤回同意收集个人信息</el-text>
    <div class="content">
      <el-text type="info">撤回同意</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
  <el-dialog
    v-model="showWithdrawDialog"
    title="撤回同意收集个人信息"
    :append-to-body="true"
    class="account-withdraw-confirm-dialog"
  >
    <h2>重要提醒</h2>
    <el-text type="info">
      此操作会退出账号，并清除您在服务器端的个人信息，除非法律法规有其他要求。
    </el-text>
    <h3>1、撤回同意收集个人信息，您将无法登陆</h3>
    <el-text type="info">
      当您撤回同意后，我们后续将不再收集和使用您相应的个人信息，<strong>包括用于登陆的手机号</strong>，当您撤回同意或授权后，我们将无法继续为您提服务。
    </el-text>
    <h3>2、请您妥善保存数据</h3>
    <el-text type="info">
      撤回同意收集个人信息，无法登陆，将导致您无法访问账户和账户中数据。请妥善保存您的数据。
    </el-text>
    <h3>3、我们将在15日之内删除数据</h3>
    <el-text type="info">
      撤回后，我们将删除账号，并在15日内完成账号上数据删除和匿名化处理。
    </el-text>
    <el-text type="danger" tag="p">
      请按下方的“确定撤回账号”按钮，即表示您已阅读并同意
    </el-text>
    <template #footer>
      <span>
        <el-button @click="cancelWithdraw">取消</el-button>
        <el-button
          type="danger"
          :loading="loading"
          :disabled="preventMistakeCountdown > 0"
          @click="confirmWithdraw"
        >
          <span v-if="preventMistakeCountdown" style="margin-right: 5px">
            {{ preventMistakeCountdown }}
          </span>
          确认撤回
        </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="showCodeDialog"
    :fullscreen="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :show-close="false"
  >
    <SmsCodeDialog
      :phone="phonePlaceHolder"
      :pre-count-down="preCountDown"
      :confirm-button-text="'撤回同意'"
      :parent-loading="loading || withdrawLoading"
      :intercept-code-resend="true"
      @do-code-action="doWithdrawRequest"
      @do-code-resend="confirmWithdraw"
      @close-dialog="cancelWithdraw"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, ref } from "vue";
import { ElMessage } from "element-plus";
import SmsCodeDialog from "@/components/Profile/LoginPage/SmsCodeDialog.vue";
import {
  fetchUserDeleteAuth,
  fetchUserDeleteConfirm,
} from "@/components/Profile/Requests/FetchUserAPI";

const emits = defineEmits(["logout"]);

const preventMistakeCountdown = ref(0);
const preventMistakeCountdownTimer = ref();
const showWithdrawDialog = ref(false);
const showCodeDialog = ref(false);
const loading = ref(false);
const phonePlaceHolder = ref("");
const preCountDown = ref(0);
const withdrawLoading = ref(false);

function btnWithdraw() {
  showWithdrawDialog.value = true;

  if (preventMistakeCountdownTimer.value) {
    clearInterval(preventMistakeCountdownTimer.value);
  }

  preventMistakeCountdown.value = 15;
  preventMistakeCountdownTimer.value = setInterval(() => {
    preventMistakeCountdown.value -= 1;
    if (preventMistakeCountdown.value <= 0) {
      clearInterval(preventMistakeCountdownTimer.value);
    }
  }, 1000);
}

function cancelWithdraw() {
  showWithdrawDialog.value = false;
  showCodeDialog.value = false;
  preCountDown.value = 0;
  phonePlaceHolder.value = "";
  loading.value = false;
  withdrawLoading.value = false;

  if (preventMistakeCountdownTimer.value) {
    clearInterval(preventMistakeCountdownTimer.value);
  }
  preventMistakeCountdown.value = 0;
}

function confirmWithdraw() {
  // 执行注销账号的操作
  loading.value = true;
  fetchUserDeleteAuth()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("验证码已发送，请注意查收");
        showWithdrawDialog.value = false;
        showCodeDialog.value = true;
        preCountDown.value = 0;
        phonePlaceHolder.value = data.data?.phone ?? "";
      } else if (data.resCode && data.resCode === "HAS_SEND_CODE") {
        ElMessage.error(data.message);
        showWithdrawDialog.value = false;
        showCodeDialog.value = true;
        preCountDown.value = data.data?.countDown ?? 0;
        phonePlaceHolder.value = data.data?.phone ?? "";
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("发送验证码失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("发送验证码失败，请稍后重试");
    })
    .finally(() => {
      loading.value = false;
    });
}

function doWithdrawRequest(_phone: string, code: string) {
  withdrawLoading.value = true;
  fetchUserDeleteConfirm(code)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("注销成功");
        cancelWithdraw();
        emits("logout");
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("注销失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("注销失败，请稍后重试");
    })
    .finally(() => {
      withdrawLoading.value = false;
    });
}

onBeforeUnmount(() => {
  if (preventMistakeCountdownTimer.value) {
    clearInterval(preventMistakeCountdownTimer.value);
  }
});
</script>

<style lang="scss" scoped>
div {
  &.parent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  &.content {
    display: flex;
    align-items: center;

    & > * {
      margin-left: 5px;
    }
  }
}

:global(.account-withdraw-confirm-dialog) {
  width: 768px;

  @media screen and (max-width: 768px) {
    width: 80%;
  }
}
</style>
