<template>
  <el-dropdown-item divided @click.stop>
    <el-checkbox v-model="showResultRect" @change="handleShowResultRectChange">
      显示识别框
    </el-checkbox>
  </el-dropdown-item>
  <el-dropdown-item @click.stop>
    <el-checkbox
      v-model="fixedHeaderMenu"
      @change="handleFixedHeaderMenuChange"
    >
      固定顶部菜单
    </el-checkbox>
  </el-dropdown-item>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElCheckbox } from "element-plus";
import {
  getShowResultRect,
  setShowResultRect,
  getFixedHeaderMenu,
  setFixedHeaderMenu,
} from "@/lib/ConfigDatabase.ts";

const showResultRect = ref(true);
const fixedHeaderMenu = ref(false);

onMounted(() => {
  readShowResultRect();
  readFixedHeaderMenu();
});

async function readShowResultRect() {
  showResultRect.value = await getShowResultRect();
}

async function readFixedHeaderMenu() {
  fixedHeaderMenu.value = await getFixedHeaderMenu();
}

const handleShowResultRectChange = (value: boolean | string | number) => {
  if (typeof value === "boolean") {
    setShowResultRect(value).then(() => {
      readShowResultRect();
    });
  }
};

const handleFixedHeaderMenuChange = (value: boolean | string | number) => {
  if (typeof value === "boolean") {
    setFixedHeaderMenu(value).then(() => {
      readFixedHeaderMenu();
    });
  }
};
</script>
