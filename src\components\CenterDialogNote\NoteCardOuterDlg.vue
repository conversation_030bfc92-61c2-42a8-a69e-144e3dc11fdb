<template>
  <!-- 内容跟NoteCardOuter.vue 大部分一样。这里的内容是在弹窗的tab中显示，需要修改样式 -->
  <!-- card笔记构成的内容有变化：
    1.头部第一行 是复制的原文，ocrContent 不是原来的标题字段。原来标题字段当知识点，新增了一个知识点，可编辑，用于搜索的条件之一
    2.第二部分原文句子，比如选了一个单词，扩选出上下文 识别出整个句子。识别之后也可编辑。（还不确定能不能实现）
    3.第三部分是高亮颜色。就是之前的card笔记颜色，增加了文字备注和自定义
    4.第四部分是知识点，可编辑。可用于搜索条件之一。
    5.第五部分是添加笔记内容，可编辑，可添加图片，可添加画板。跟以前一样。
   -->
  <el-card
    v-loading="loading"
    ref="noteCardRef"
    :body-style="{ padding: '0px' }"
    shadow="never"
    class="box-card"
  >
    <div>
      <NoteCardTitle
        :title="noteContent.noteTitle"
        :color="localRect.color"
        :readonly="!cardEdit"
      />
    </div>
    <div :class="{ 'note-card': cardEdit }">
      <NoteCardContent
        :origin-text="noteContent.ocrContent"
        :origin-text-span="noteContent.ocrContentSpan"
        :readonly="!cardEdit"
        @text-changed="onOcrContentChanged"
      />
      <div>
        <ColorTags
          v-if="!isVerticalFullscreen && cardEdit"
          :color="localRect.color"
          @color-change="colorChange"
        />
      </div>
      <div
        v-if="!isVerticalFullscreen && !hideKeywordTag"
        style="display: flex; align-items: center"
      >
        <label>标签：</label>
        <NoteCardTagText
          :keyword="noteContent.keyword"
          :readonly="!cardEdit"
          :is-vertical="!isLandscape"
          style="flex: 1; margin-left: 5px"
          @keyword-changed="onKeywordChanged"
        />
      </div>
      <div>
        <Teleport
          v-if="!hideNoteContent"
          :to="`#r-${rect.uid}`"
          :disabled="!(isLandscape && cardEdit)"
        >
          <div style="display: flex; align-items: center">
            <label>笔记：</label>
            <NoteCardNewItem
              v-if="cardEdit"
              :drawingEnabled="drawingEnabled"
              @add-item="addItem"
              @toggle-drawing="toggleDrawing"
            />
          </div>
          <div>
            <div
              v-for="item in noteContent.noteContentArray"
              :key="item.uid"
              style="position: relative"
            >
              <div v-if="item.type === 'TEXT' && !isVerticalFullscreen">
                <NoteCardText
                  :card-edit="cardEdit && !drawingEnabled"
                  :item="item"
                  @update-item="updateItem"
                />
              </div>
              <div v-if="item.type === 'IMAGE'">
                <NoteCardImage
                  :card-edit="cardEdit && drawingEnabled"
                  :item="item"
                  @update-item="updateItem"
                  @delete-item="handleDeleteItem"
                />
              </div>
              <div v-if="item.type === 'DRAWING'">
                <NoteFreeDrawing
                  :item="item"
                  :last-uid="lastItemUid"
                  :edit-enabled="cardEdit && drawingEnabled"
                  :disable-scroll="true"
                  @update-item="updateItem"
                  @delete-item="handleDeleteItem"
                />
              </div>
              <div
                v-if="cardEdit && !drawingEnabled"
                style="position: absolute; right: 8px; top: 2px; z-index: 100"
              >
                <el-button text size="small" @click="handleDeleteItem(item)">
                  <i class="bi bi-trash" />
                </el-button>
              </div>
            </div>
          </div>
        </Teleport>
        <Teleport v-if="cardEdit" :to="`#rbl-${rect.uid}`">
          <div class="fixed-bottom">
            <NoteCardActions
              :fullscreen="fullscreen"
              :drawingEnabled="drawingEnabled"
              @add-item="addItem"
              @toggle-drawing="toggleDrawing"
              @toggle-fullscreen="toggleFullscreen('manual')"
              @delete-note="sendDeleteRectMessage"
            />
            <!-- <div>
            <el-divider style="margin: 5px 0px 5px 0px" />
            <NoteCardTags
              :card-edit="cardEdit"
              :noteTags="noteContent.noteTags"
              @update-tags="updateTags"
            />
          </div> -->
          </div>
        </Teleport>
        <Teleport v-if="cardEdit" :to="`#rbv-${rect.uid}`">
          <div class="fixed-bottom">
            <NoteCardActions
              :fullscreen="fullscreen"
              :drawingEnabled="drawingEnabled"
              @add-item="addItem"
              @toggle-drawing="toggleDrawing"
              @toggle-fullscreen="toggleFullscreen('manual')"
              @delete-note="sendDeleteRectMessage"
            />
            <!-- <div>
            <el-divider style="margin: 5px 0px 5px 0px" />
            <NoteCardTags
              :card-edit="cardEdit"
              :noteTags="noteContent.noteTags"
              @update-tags="updateTags"
            />
          </div> -->
          </div>
        </Teleport>
        <Teleport to="body">
          <NoteFreeDrawingPenToolbox
            v-if="showPenToolbox"
            :fullscreen="fullscreen"
            :landscape="isLandscape"
            @toggle-drawing="toggleDrawing"
          />
        </Teleport>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import _ from "lodash";
import { computed, nextTick, ref, watch } from "vue";
import { useElementVisibility } from "@vueuse/core";
import { useObservable } from "@vueuse/rxjs";
import { liveQuery } from "dexie";
import { pinyin } from "pinyin-pro";
import {
  // deleteRectByUID,
  getNoteFromRect,
  saveNote,
  updateChildRectsColor,
} from "@/lib/RectDatabase.js";
import NoteCardContent from "@/components/CenterDialogNote/NoteCard/NoteCardContent.vue";
import NoteCardTagText from "@/components/CenterDialogNote/NoteCard/NoteCardTagText.vue";
import NoteCardText from "@/components/CenterDialogNote/NoteCard/NoteCardText.vue";
import NoteCardImage from "@/components/NotebookPanel/NoteCardCompt/NoteCardImage.vue";
// import NoteCardTags from "@/components/NotebookPanel/NoteCardCompt/NoteCardTags.vue";
import NoteCardNewItem from "@/components/NotebookPanel/NoteCardCompt/NoteCardNewItem.vue";
import NoteCardActions from "@/components/NotebookPanel/NoteCardCompt/NoteCardActions.vue";
import NoteFreeDrawing from "@/components/NotebookPanel/NoteCardCompt/NoteFreeDrawing.vue";
import NoteFreeDrawingPenToolbox from "@/components/NotebookPanel/NoteCardCompt/NoteFreeDrawingPenToolbox.vue";
import ColorTags from "@/components/CenterDialogNote/NoteCard/ColorTags.vue";
import NoteCardTitle from "@/components/CenterDialogNote/NoteCard/NoteCardTitle.vue";

const props = defineProps({
  rect: {
    type: Object,
    required: true,
  },
  cardEdit: {
    type: Boolean,
    default: true,
  },
  listMode: {
    type: Boolean,
    default: false,
  },
  fullscreen: {
    type: Boolean,
    default: false,
  },
  isLandscape: {
    type: Boolean,
    default: false,
  },
  isNew: {
    type: Boolean,
    default: false,
  },
});

const localRect = ref({ ...props.rect });

const emits = defineEmits(["delete-item", "toggle-fullscreen"]);

const loading = ref(true);
const noteCardRef = ref(null);
const isNewNote = ref(false);
const drawingEnabled = ref(false);
const preDrawingFullscreenStatus = ref(null);
const noteContent = useObservable(
  liveQuery(() => getNoteFromRect(props.rect.uid)),
  {
    initialValue: {
      noteTitle: "",
      ocrContent: "",
      ocrContentSpan: "",
      keyword: "",
      color: "",
      noteContentArray: [],
      noteTags: [],
    },
  }
);

const noteCardVisible = useElementVisibility(noteCardRef);
// 是否是竖屏画笔状态，竖屏画笔状态下，某些字段不显示
const isVerticalFullscreen = computed(
  () => props.cardEdit && drawingEnabled.value && !props.isLandscape
);
// 是否显示画笔
const showPenToolbox = computed(
  () => drawingEnabled.value && (props.isLandscape || noteCardVisible.value)
);

watch(
  () => props.isNew,
  () => (isNewNote.value = props.isNew),
  {
    immediate: true,
  }
);

watch(
  () => noteContent.value,
  () => {
    if (noteContent.value.uid && isNewNote.value) {
      const hasTextItem = noteContent.value.noteContentArray.some(
        (item) => item.type === "TEXT"
      );
      if (!hasTextItem) {
        nextTick(() => {
          addItem({ type: "TEXT", value: "", isNew: true });
          isNewNote.value = false;
        });
      }
    }
  }
);

watch(
  () => props.isLandscape,
  () => {
    toggleFullscreen("oriental");
  }
);

watch(
  () => noteContent.value.uid,
  () => {
    if (noteContent.value.uid) {
      loading.value = false;
    }
  },
  { immediate: true }
);

const lastItemUid = computed(() => {
  if (noteContent.value.noteContentArray.length === 0) {
    return null;
  }

  if (props.cardEdit && drawingEnabled.value) {
    const lastItem = _.findLast(
      noteContent.value.noteContentArray,
      (item) => item.type === "DRAWING"
    );
    return lastItem?.uid ?? null;
  }

  return noteContent.value.noteContentArray[
    noteContent.value.noteContentArray.length - 1
  ].uid;
});

const hideKeywordTag = computed(() => {
  return !props.cardEdit && !noteContent.value.keyword;
});

const hideNoteContent = computed(() => {
  const arrayIsEmpty = noteContent.value.noteContentArray.length === 0;
  const arrayContentIsEmpty = noteContent.value.noteContentArray.every(
    (item) => !item.value
  );
  return !props.cardEdit && (arrayIsEmpty || arrayContentIsEmpty);
});

function onOcrContentChanged(text, spanText) {
  noteContent.value.ocrContent = text;
  noteContent.value.ocrContentSpan = spanText;
  if (noteContent.value.uid) {
    saveNote(noteContent.value);
  }
}

function onKeywordChanged(text) {
  noteContent.value.keyword = text;
  const alphabets = pinyin(text, {
    toneType: "none",
    pattern: "first",
    nonZh: "consecutive",
  });
  if (alphabets) {
    // 获取字符串中的第一个英文字母或者数字
    const reg = /[a-zA-Z0-9]/;
    const match = alphabets.match(reg);
    noteContent.value.keywordAlphabet = match ? match[0].toUpperCase() : "";
  } else {
    noteContent.value.keywordAlphabet = "";
  }
  if (noteContent.value.uid) {
    saveNote(noteContent.value);
  }
}

function addItem(item) {
  if (drawingEnabled.value && item.type !== "DRAWING") {
    return;
  }

  if (
    item.type === "DRAWING" &&
    noteContent.value.noteContentArray.some((item) => item.type === "DRAWING")
  ) {
    return;
  }

  // if last item is empty, replace it
  if (noteContent.value.noteContentArray.length > 0) {
    const lastItem =
      noteContent.value.noteContentArray[
        noteContent.value.noteContentArray.length - 1
      ];
    if (lastItem.value === "") {
      noteContent.value.noteContentArray = _.slice(
        noteContent.value.noteContentArray,
        0,
        noteContent.value.noteContentArray.length - 1
      );
    }
  }

  noteContent.value.noteContentArray =
    noteContent.value.noteContentArray.concat({ uid: Date.now(), ...item });
  if (noteContent.value.uid) {
    saveNote(noteContent.value);
  }
}

function updateItem(item) {
  noteContent.value.noteContentArray = noteContent.value.noteContentArray.map(
    (i) => (i.uid === item.uid ? item : i)
  );
  if (noteContent.value.uid) {
    saveNote(noteContent.value);
  }
}

function handleDeleteItem(item) {
  noteContent.value.noteContentArray =
    noteContent.value.noteContentArray.filter((i) => i.uid !== item.uid);
  if (noteContent.value.uid) {
    saveNote(noteContent.value);
  }
}

function toggleDrawing() {
  if (props.cardEdit) {
    drawingEnabled.value = !drawingEnabled.value;
    if (drawingEnabled.value) {
      preDrawingFullscreenStatus.value = props.fullscreen;
    }
    // 根据画笔开关，切换全屏或横屏适配
    toggleFullscreen("draw");
    if (!drawingEnabled.value) {
      preDrawingFullscreenStatus.value = null;
      return;
    }
    if (
      noteContent.value.noteContentArray.length === 0 ||
      noteContent.value.noteContentArray[
        noteContent.value.noteContentArray.length - 1
      ].type !== "DRAWING"
    ) {
      addItem({ uid: Date.now(), type: "DRAWING", value: "" });
    }
  }
}

// function updateTags(tags) {
//   noteContent.value.noteTags = tags;
//   if (noteContent.value.uid) {
//     saveNote(noteContent.value);
//   }
// }

/**
 * @type {string} type - 触发类型，manual 手动触发，oriental 方向触发, draw 画笔触发
 *
 */
function toggleFullscreen(type) {
  if (drawingEnabled.value) {
    // 竖屏时强制全屏
    if (!props.isLandscape) {
      emits("toggle-fullscreen", true);
    } else {
      if (type === "manual") {
        emits("toggle-fullscreen", !props.fullscreen);
      }
    }
  } else {
    // 非手写状态下只能manual触发
    if (type === "manual") {
      emits("toggle-fullscreen", !props.fullscreen);
    } else {
      if (typeof preDrawingFullscreenStatus.value === "boolean") {
        emits("toggle-fullscreen", preDrawingFullscreenStatus.value);
      } else {
        emits("toggle-fullscreen", props.fullscreen);
      }
    }
  }
}

function sendDeleteRectMessage() {
  emits("delete-item");
  // deleteRectByUID(props.rect.uid);
}

// 修改并同步颜色
function colorChange(colorTag) {
  localRect.value.color = colorTag.color;
  noteContent.value.color = colorTag.color;
  noteContent.value.colorTagUid = colorTag.id;

  // 替换ocrContentSpan中的颜色
  if (colorTag.color && noteContent.value.ocrContentSpan) {
    const reg =
      /color: #[0-9a-fA-F]{6}|color: rgb\(\d{1,3}, \d{1,3}, \d{1,3}\)/g;
    noteContent.value.ocrContentSpan = noteContent.value.ocrContentSpan.replace(
      reg,
      `color: ${colorTag.color}`
    );
    if (noteContent.value.uid) {
      saveNote(noteContent.value);
    }
  }

  updateChildRectsColor(props.rect.fileId, props.rect.uid, colorTag.color);
}
</script>

<style lang="scss" scoped>
.box-card {
  position: relative;
  padding: 0;
  border-radius: 8px;
  width: 100%;
  border: none;
  background-color: #fdfaf3;
}

.fixed-bottom {
  background-color: #fdfaf3;
  z-index: 1;
}

.note-card {
  height: 100%;
  display: flex;
  flex-flow: column;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.note-card:hover {
  scrollbar-color: rgba(0, 0, 0, 0.5) transparent;
}
</style>
