<template>
  <div class="headerButtonStart" @click="changeTool('HAND')">
    <img
        v-if="aliveTool === 'HAND'"
        alt=""
        class="btn-square-borderNone-large"
        src="@/assets/image/hand2_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone-large"
        src="@/assets/image/hand2.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('PEN')">
    <img
        v-if="aliveTool === 'PEN'"
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/pen2_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/pen2.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('HIGHLIGHT_PEN')">
    <img
        v-if="aliveTool === 'HIGHLIGHT_PEN'"
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/lightpen_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/lightpen.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('LINE')">
    <img
        v-if="aliveTool === 'LINE'"
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/strline_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/strline.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('DOTTED_LINE')">
    <img
        v-if="aliveTool === 'DOTTED_LINE'"
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/line2_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/line2.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('ERASER')">
    <img
        v-if="aliveTool === 'ERASER'"
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/move_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/move.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('BOX_SELECT')">
    <img
        v-if="aliveTool === 'BOX_SELECT'"
        alt=""
        class="btn-square-borderNone-large"
        src="@/assets/image/copyword_red.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone-large"
        src="@/assets/image/copyword.png"
    />
  </div>
  <div class="headerButton" @click="changeTool('ADD_TEXT')">
    <img
        v-if="aliveTool === 'ADD_TEXT'"
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/font_2.png"
    />
    <img
        v-else
        alt=""
        class="btn-square-borderNone"
        src="@/assets/image/font_1.png"
    />
  </div>
<!--  <div class="headerButton" @click="changeTool('ADD_IMAGE')">-->
<!--    <img-->
<!--        v-if="aliveTool === 'ADD_IMAGE'"-->
<!--        alt=""-->
<!--        class="btn-square-borderNone"-->
<!--        src="@/assets/image/picture_red.png"-->
<!--    />-->
<!--    <img-->
<!--        v-else-->
<!--        alt=""-->
<!--        class="btn-square-borderNone"-->
<!--        src="@/assets/image/picture.png"-->
<!--    />-->
<!--  </div>-->
</template>

<script setup>
defineProps({
  aliveTool: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["change-tool"]);

function changeTool(tool) {
  emit("change-tool", tool);
}
</script>

<style scoped>
.headerButton {
  height: 100%;
  align-items: center;
  justify-content: center;
  display: flex;
  padding-left: 8px;
  padding-right: 8px;
}

.headerButtonStart {
  height: 100%;
  align-items: center;
  justify-content: center;
  display: flex;
  padding-left: 25px;
  padding-right: 5px;
}

.btn-square-borderNone {
  width: 25px;
  height: 25px;
}

.btn-square-borderNone-large {
  width: 30px;
  height: 30px;
}
</style>
