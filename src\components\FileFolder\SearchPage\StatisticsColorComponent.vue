<template>
    <div style="margin-left: 10px;margin-right: 10px;padding-top: 15px;">
        <div class="btn-wrapper">
            <div v-for="(colorItem, index) in defaultColors" :key="colorItem" style="display: flex; align-items: center;">
                <div style="text-align: center;">
                    <div v-if="index < 3">
                        <div v-if="searchColors && searchColors.includes(colorItem)" @click="changeColorIndex(colorItem)">
                            <div :style="{ borderColor: colorItem }" class="btn-color-width-borderColor">
                                <div :style="{ background: colorItem }" class="btn-color-width-borderColor-inner">
                                    <span class="buttonTiele" v-if="colorItem === 'rgb(210, 19, 19)'">未掌握</span>
                                    <span class="buttonTiele" v-else-if="colorItem === 'rgb(254, 208, 48)'">学习中</span>
                                    <span class="buttonTiele" v-else-if="colorItem === 'rgb(83, 215, 105)'">已掌握</span>
                                </div>
                            </div>
                        </div>
                        <div v-else :style="{ background: colorItem }" class="btn-color-width"
                            @click="changeColorIndex(colorItem)">
                            <span class="buttonTiele" v-if="colorItem === 'rgb(210, 19, 19)'">未掌握</span>
                            <span class="buttonTiele" v-else-if="colorItem === 'rgb(254, 208, 48)'">学习中</span>
                            <span class="buttonTiele" v-else-if="colorItem === 'rgb(83, 215, 105)'">已掌握</span>
                        </div>
                    </div>
                    <div v-else>
                        <div v-if="searchColors && searchColors.includes(colorItem)" @click="changeColorIndex(colorItem)">
                            <div :style="{ borderColor: colorItem }" class="btn-square-bottom-borderColor">
                                <div :style="{ background: colorItem }" class="btn-square-bottom-borderColor-inner"></div>
                            </div>
                        </div>
                        <el-button v-else :style="{ background: colorItem }" class="btn-square-bottom"
                            @click="changeColorIndex(colorItem)"></el-button>
                    </div>
                    <span class="el-text">12</span>
                </div>
                <van-divider v-if="index<defaultColors.length-1" vertical :style="{ height: '40px', borderColor: '#c8c9cc' }" />
            </div>

        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue';
import { DefaultColors } from "@/components/PenFloatToolbox/DefaultValues";
const defaultColors = [...DefaultColors.SEARCH];
const searchColors = ref([]);

const changeColorIndex = (colorItem) => {
    if (searchColors.value && searchColors.value.includes(colorItem)) {
        searchColors.value = searchColors.value.filter(item => item !== colorItem);
    } else {
        searchColors.value.push(colorItem);
    }
    console.log("搜索颜色", searchColors.value);
}

</script>

<style scoped>
.btn-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: left;
    align-items: center;
}

.btn-square-bottom {
    width: 25px;
    height: 25px;
    border-radius: 7px;
    border: 0;
    padding: 0;
    margin: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.buttonTiele {
    font-size: 0.75rem;
    color: #fff;
}

.btn-color-width {
    width: 50px;
    height: 25px;
    border-radius: 7px;
    border: 0;
    padding: 0;
    margin: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-color-width-borderColor {
    width: 50px;
    height: 25px;
    border-radius: 7px;
    margin: 0px;
    border: 3px solid;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-color-width-borderColor-inner {
    width: 40px;
    height: 15px;
    border-radius: 5px;
}


.btn-square-bottom-borderColor {
    width: 25px;
    height: 25px;
    border-radius: 7px;
    margin: 0px;
    border: 3px solid;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-square-bottom-borderColor-inner {
    width: 15px;
    height: 15px;
    border-radius: 5px;
}
</style>
```