<template>
  <div
    v-if="
      [
        'PEN',
        'HIGHLIGHT_PEN',
        'HIGHLIGHT_PEN_STRAIGHT',
        'ERASER',
        'LINE',
        'DOTTED_LINE',
      ].includes(currentTool.type)
    "
  >
    <h4 style="margin-top: 15px">粗细：</h4>
    <div style="display: flex; flex-wrap: nowrap; align-items: center">
      <div class="slider-demo-block" style="width: 250px">
        <span class="demonstration">{{ sliderValue / divider }}</span>
        <el-slider
          v-model="sliderValue"
          :min="10"
          :format-tooltip="formatTooltip"
          @change="changePenWidth"
        />
      </div>
      <div
        :style="{
          height: (sliderValue / divider) * 2.5 + 'px',
          backgroundColor: currentTool.aliveColor || 'black',
        }"
        class="btn-line-demo"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
const props = defineProps({
  currentTool: {
    type: Object,
    default: () => ({}),
  },
});
const divider = computed(() => {
  // return props.currentTool.type === "ERASER" ? 2 : 25;
  return props.currentTool.type === "ERASER"
    ? 2
    : props.currentTool.type === "HIGHLIGHT_PEN" ||
      props.currentTool.type === "HIGHLIGHT_PEN_STRAIGHT"
    ? 10
    : 20;
});

const sliderValue = ref(0);
const emit = defineEmits(["change-line-width"]);
const changePenWidth = (newValue) => {
  emit("change-line-width", newValue / 5);
};
function formatTooltip(val) {
  return val / divider.value;
}
onMounted(() => {
  sliderValue.value = props.currentTool.lineWidth * 5;
});
</script>

<style scoped>
.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block .demonstration {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 44px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}

.slider-demo-block .demonstration + .el-slider {
  flex: 0 0 65%;
}

.btn-square-bottom {
  width: 30px;
  height: 30px;
  border-radius: 7px;
  border: 0;
  padding: 0;
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-line-demo {
  margin-left: 10px;
  display: flex;
  align-items: center;
  width: 20px;
  background-color: rgb(0, 0, 0);
  border-radius: 10px;
}

.btn-square-bottom-line-alive {
  background-color: rgb(144, 147, 153);
}
</style>
