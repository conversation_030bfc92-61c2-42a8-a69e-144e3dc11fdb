<template>
  <el-card v-loading="loading">
    <el-form
      ref="formRef"
      :model="level"
      :rules="rules"
      label-width="120px"
      status-icon
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="level.name" />
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input-number v-model.number="level.price" :min="0" />
      </el-form-item>
      <el-form-item label="时长（天）" prop="duration">
        <el-input-number v-model.number="level.duration" :min="0" />
      </el-form-item>
      <el-form-item label="可用" prop="available">
        <el-switch v-model="level.available" />
      </el-form-item>
      <el-form-item label="显示" prop="show">
        <el-switch v-model="level.show" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input type="textarea" v-model="level.description" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button v-if="props.id" type="danger" @click="deleteLevel">
          删除
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import {
  fetchLevelDetails,
  fetchDeleteLevel,
  fetchAddLevel,
  fetchUpdateLevel,
} from "@/components/AdminPanel/SubPanel/FetchAdminAPI";

const props = defineProps({
  id: {
    type: String,
    required: false,
  },
});

const emits = defineEmits(["update", "delete"]);

type Level = {
  id?: string;
  name: string;
  price: number;
  duration: number;
  available: boolean;
  show: boolean;
  description: string;
};

const rules = reactive<FormRules<Level>>({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  price: [
    { required: true, message: "请输入价格", trigger: "blur" },
    { type: "number", message: "价格必须为数字", trigger: "blur" },
    {
      validator: (_, value) => value > 0,
      message: "价格不能小于0",
      trigger: "blur",
    },
  ],
  duration: [
    { required: true, message: "请输入时长", trigger: "blur" },
    { type: "number", message: "时长必须为数字", trigger: "blur" },
    {
      validator: (_, value) => value > 0,
      message: "时长不能小于0",
      trigger: "blur",
    },
  ],
});

const loading = ref(false);
const formRef = ref<FormInstance>();
const defaultLevel = ref<Level>({
  name: "",
  price: 0,
  duration: 0,
  available: true,
  show: true,
  description: "",
});
const level = reactive<Level>({ ...defaultLevel.value });

onMounted(() => {
  if (props.id) {
    loading.value = true;
    fetchLevelDetails(props.id)
      .then((data) => {
        if (data.resCode && data.resCode === "SUCCESS") {
          const levelData = data.data?.level;
          if (levelData) {
            Object.assign(level, {
              ...levelData,
              price: Number(levelData.price),
            });

            defaultLevel.value = levelData;
            defaultLevel.value.price = Number(levelData.price);
          }
        } else if (data.message) {
          ElMessage.error(data.message);
        }
      })
      .catch(() => {
        ElMessage.error("获取数据失败");
      })
      .finally(() => {
        loading.value = false;
      });
  }
});

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (props.id) {
        fetchUpdateLevel(props.id, level)
          .then((data) => {
            if (data.resCode && data.resCode === "SUCCESS") {
              const levelData = data.data?.level;
              if (levelData) {
                Object.assign(level, {
                  ...levelData,
                  price: Number(levelData.price),
                });

                defaultLevel.value = levelData;
                defaultLevel.value.price = Number(levelData.price);
              }

              ElMessage.success("更新成功");
              emits("update");
            } else if (data.message) {
              ElMessage.error(data.message);
            }
          })
          .catch(() => {
            ElMessage.error("更新失败");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        fetchAddLevel(level)
          .then((data) => {
            if (data.resCode && data.resCode === "SUCCESS") {
              const levelData = data.data?.level;
              if (levelData) {
                Object.assign(level, {
                  ...levelData,
                  price: Number(levelData.price),
                });

                defaultLevel.value = levelData;
                defaultLevel.value.price = Number(levelData.price);
              }

              ElMessage.success("添加成功");
              emits("update");
            } else if (data.message) {
              ElMessage.error(data.message);
            }
          })
          .catch(() => {
            ElMessage.error("添加失败");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    } else {
      ElMessage.error("表单验证失败");
    }
  });
};

const resetForm = () => {
  if (!formRef.value) return;
  Object.assign(level, { ...defaultLevel.value });
  formRef.value.clearValidate();
};

function deleteLevel() {
  if (!props.id) return;
  loading.value = true;
  fetchDeleteLevel(props.id)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("删除成功");
        emits("delete");
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("删除失败");
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
