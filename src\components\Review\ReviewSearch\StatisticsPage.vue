<template>
    <van-nav-bar title="统计" left-text="返回" left-arrow @click-left="goBack" />
    <div v-if="searchTypeOut === 'note'">
        <StatisticsColorComponent />
        <van-row>
            <van-col span="8">
                <StatisticsTagComponent />
            </van-col>
            <van-col span="16">
                <StatisticshNoteList />
            </van-col>
        </van-row>
    </div>
    <van-sticky :offset-bottom="30" position="bottom">
        <div style="padding-left: 30px;padding-right: 30px;">
            <van-button type="primary" block  @click="onLearn">开始学习</van-button>        
        </div>
    </van-sticky>
</template>
  
<script setup>
import { useRouter } from 'vue-router';
import { ref } from 'vue';

import StatisticsColorComponent from "@/components/FileFolder/SearchPage/StatisticsColorComponent.vue";
import StatisticsTagComponent from "@/components/FileFolder/SearchPage/StatisticsTagComponent.vue";
import StatisticshNoteList from "@/components/FileFolder/SearchPage/StatisticshNoteList.vue";


const router = useRouter();
const searchKeyWord = ref('');
const searchTypeOut = ref('note');

const onLearn = () => {
    console.log('查看筛选的学习卡');
}
const goBack = () => {
    router.back();
}
</script>

<style scoped></style>
```