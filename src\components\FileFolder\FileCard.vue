<template>
  <el-card
    ref="cardRef"
    :body-style="{ padding: '0px' }"
    class="el-card"
    shadow="never"
  >
    <!--    <div style="display: flex;justify-content: flex-end;">-->
    <!--      <van-tag v-if="isFolder" style="text-align: right;" type="success">{{ file.notesCount }}</van-tag>-->
    <!--      <van-tag v-else style="text-align: right;" type="primary">{{ file.notesCount }}</van-tag>-->
    <!--    </div>-->
    <div
      v-loading="!isFileCoverLoaded"
      class="image-container"
      @click="onFileClick"
    >
      <div v-if="isFolder" style="display: flex; justify-content: center">
        <el-icon :size="100">
          <Folder />
        </el-icon>
      </div>
      <el-image
        v-else
        :src="fileImage"
        fit="cover"
        loading="lazy"
        class="image"
        alt=""
      />
    </div>
    <div style="padding: 8px">
      <van-text-ellipsis
        :content="file.name"
        class="text-filename"
        position="middle"
        rows="2"
        @click="onFileClick"
      />
      <div class="bottom">
        <time class="time">{{ lastModified }}</time>
        <van-popover
          v-model:show="showPopover"
          :actions="actions"
          @select="onSelect"
        >
          <template #reference>
            <van-icon name="more-o" />
          </template>
        </van-popover>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed, ref, toRefs } from "vue";
import dayjs from "dayjs";
import { useElementVisibility, watchOnce } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { Folder } from "@element-plus/icons-vue";
import {
  deleteFileMeta,
  getFileMeta,
  putFileMeta,
  updateParentFolderFileCount,
  readFileCover,
} from "@/lib/FileList.js";
import { useFileFolderStore } from "@/stores/file-folder";
import { showAskFilenameDialog } from "@/components/FileFolder/Helper/AskFilenameDialog.jsx";
import { showFolderTreeDialog } from "@/components/FileFolder/FolderTree/FolderTreeDialog.jsx";
import CactusPlaceholder from "@/assets/image/cactus.png";

const props = defineProps({
  file: {
    type: Object,
    required: true,
  },
});
const emits = defineEmits(["on-file-click"]);

const cardRef = ref();
const cardIsVisible = useElementVisibility(cardRef);
const { file } = toRefs(props);
const fileImage = ref(CactusPlaceholder);
const isFileCoverLoaded = ref(false);
const fileFolderStore = useFileFolderStore();
const showPopover = ref(false);

watchOnce(cardIsVisible, () => {
  loadFileImage();
});

async function loadFileImage() {
  const file = props.file;
  if (!file || file.type === "folder") {
    isFileCoverLoaded.value = true;
    return;
  }

  const fileCover = fileFolderStore.fileCoverList[file.id];
  if (fileCover) {
    fileImage.value = fileCover;
    isFileCoverLoaded.value = true;
    return;
  }

  if (file.image) {
    fileImage.value = file.image;
  } else {
    const coverData = await readFileCover(file.id);
    if (coverData) {
      fileImage.value = coverData.cover;
      fileFolderStore.fileCoverList[file.id] = coverData.cover;
    }
  }

  isFileCoverLoaded.value = true;
}

const actions = computed(() => {
  if (props.file && props.file.type === "folder") {
    return [
      { text: "重命名", icon: "edit" },
      { text: "移动", icon: "exchange" },
      { text: "删除", icon: "delete-o" },
    ];
  } else if (props.file && props.file.type === "file") {
    return [
      // { text: "分享", icon: "share-o" },
      // { text: "导出", icon: "description-o" },
      { text: "重命名", icon: "edit" },
      { text: "移动", icon: "exchange" },
      { text: "删除", icon: "delete-o" },
      // { text: "统计", icon: "info-o" },
    ];
  }

  return [];
});

const isFolder = computed(() => {
  return file.value && file.value.type === "folder";
});

const lastModified = computed(() => {
  if (!file.value || !file.value.lastModified) {
    return "";
  }

  const timestamp = Number(file.value.lastModified);
  if (dayjs(timestamp).isSame(dayjs(), "day")) {
    return dayjs(timestamp).format("HH:mm");
  } else {
    return dayjs(timestamp).format("YYYY/MM/DD");
  }
});

function onFileClick() {
  emits("on-file-click", props.file);
}

function onSelect(item) {
  if (item.text === "分享") {
    console.log("share");
  } else if (item.text === "导出") {
    console.log("export");
  } else if (item.text === "移动") {
    moveFile();
  } else if (item.text === "删除") {
    deleteFile();
  } else if (item.text === "重命名") {
    renameFile();
  } else if (item.text === "统计") {
    console.log("stat");
  }
}

async function renameFile() {
  const fileMeta = await getFileMeta(props.file.id);
  if (!fileMeta) {
    return;
  }
  const type = fileMeta.type === "folder" ? "文件夹" : "文件";
  const newName = await showAskFilenameDialog(type, fileMeta.name);
  if (newName) {
    fileMeta.name = newName;
    await putFileMeta(fileMeta);
    ElMessage.success("重命名成功");
  }
}

async function deleteFile() {
  const fileMeta = await getFileMeta(props.file.id);
  if (!fileMeta) {
    return;
  }
  // 当前文件是文件夹，则删除所有子文件
  const idList = await deleteFileMeta(fileMeta);
  await updateParentFolderFileCount(fileMeta, -1);
  ElMessage.success("删除成功");
  if (typeof ReactNativeWebView !== "undefined") {
    const data = {
      action: "delete-multi-file",
      timestamp: idList.flat(Infinity).join(","),
    };
    // eslint-disable-next-line no-undef
    ReactNativeWebView.postMessage(JSON.stringify(data));
  }
}

async function moveFile() {
  const targetNodes = await showFolderTreeDialog();
  await moveFileTo(targetNodes);
}

async function moveFileTo(targetNodes) {
  const fileMeta = await getFileMeta(props.file.id);
  const targetNode = targetNodes[targetNodes.length - 1];
  if (
    !fileMeta ||
    targetNode?.id === fileMeta.parentId ||
    targetNode?.id === fileMeta.id
  ) {
    return;
  }
  // 移动到子目录，不能移动到自己或自己的子目录
  const isChild = targetNodes.some((node) => node.parentId === fileMeta.id);
  if (isChild) {
    ElMessage.error("不能移动到子目录");
    return;
  }
  const oldParentId = fileMeta.parentId;
  if (targetNodes.length === 0) {
    // 移动到根目录，则直接移动
    fileMeta.parentId = "";
    await putFileMeta(fileMeta);
  } else {
    const targetFolderMeta = await getFileMeta(targetNode.id);
    if (!targetFolderMeta) {
      ElMessage.error("目标文件夹不存在");
      return;
    }
    // 移动到目标文件夹
    fileMeta.parentId = targetFolderMeta.id;
    await putFileMeta(fileMeta);
    // 更新目标文件夹文件数加1
    targetFolderMeta.notesCount++;
    await putFileMeta(targetFolderMeta);
  }

  // 原父目录的文件数减1
  if (oldParentId) {
    const oldParent = await getFileMeta(oldParentId);
    if (oldParent) {
      oldParent.notesCount--;
      await putFileMeta(oldParent);
    }
  }
}
</script>
<style scoped>
.el-card {
  margin: 8px;
  border: none;
}

.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio: 85 / 110;
  box-shadow: var(--el-box-shadow-light);
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.text-filename {
  padding: 0;
  display: flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  min-height: 42px;
}

.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time {
  font-size: 12px;
  padding-left: 5px;
  color: #999;
}

.button-more {
  font-size: 12px;
  color: #999;
}
</style>
