import {ref, onMounted, onBeforeUnmount} from "vue";
import { OCRClient } from 'tesseract-wasm';

export default function useOcrReader() {
    const ocrWorker = ref();

    async function initOcrWorker() {
        ocrWorker.value = new OCRClient({
            workerURL: '/tesseractjs/wasm/tesseract-worker.js',
        });

        await ocrWorker.value.loadModel(
            "/tesseractjs/wasm/eng.fast.traineddata"
        );
    }

    onMounted(() => initOcrWorker());
    onBeforeUnmount(() => ocrWorker.value?.destroy());

    return {
        ocrWorker,
    }
}