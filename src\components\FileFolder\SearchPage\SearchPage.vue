<template>
  <van-nav-bar left-arrow left-text="返回" title="搜索" @click-left="goBack"/>
  <SearchComponent :keyWord="searchKeyWord" :searchType="searchTypeOut" @onSearch="onSearch"/>
  <div v-if="searchTypeOut === 'note'">
    <SearchColorComponent/>
    <van-row>
      <van-col span="8">
        <SearchTagComponent/>
      </van-col>
      <van-col span="16">
        <SearchNoteList/>
      </van-col>
    </van-row>
  </div>
  <div v-else>
    <FileCardList />
  </div>
</template>

<script setup>
import {ref} from 'vue';
import {useRouter} from 'vue-router';
import SearchComponent from "@/components/FileFolder/SearchPage/SearchComponent.vue";
import SearchColorComponent from "@/components/FileFolder/SearchPage/SearchColorComponent.vue";
import SearchTagComponent from "@/components/FileFolder/SearchPage/SearchTagComponent.vue";
import SearchNoteList from "@/components/FileFolder/SearchPage/SearchNoteList.vue";
import FileCardList from "@/components/FileFolder/FileCardList.vue";

const router = useRouter();
const searchKeyWord = ref('');
const searchTypeOut = ref('note');

const onSearch = ({searchType, keyWord}) => {
  searchTypeOut.value = searchType.value;
  searchKeyWord.value = keyWord.value;
  console.log('搜索:', searchType.value, keyWord.value);
}

const onCancel = () => {
  console.log('取消搜索');
}

const goBack = () => {
  router.back();
}
</script>

<style scoped>

</style>