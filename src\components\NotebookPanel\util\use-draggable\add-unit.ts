export function addUnit(value?: string | number, defaultUnit = 'px') {
    if (!value) return ''
    if (isNumber(value) || isStringNumber(value)) {
        return `${value}${defaultUnit}`
    } else if (isString(value)) {
        return value
    }
}

export const isNumber = (val: any): val is number => typeof val === 'number';

export const isStringNumber = (val: string): boolean => {
    if (!isString(val)) {
        return false
    }
    return !Number.isNaN(Number(val))
}

export declare const isString: (val: unknown) => val is string;