<template>
  <div
    ref="noteFreeDrawingRef"
    :style="activeStyle"
    style="
      width: 100%;
      position: relative;
      margin-top: 10px;
      margin-bottom: 10px;
    "
  >
    <v-stage
      ref="stage"
      :config="stageConfig"
      @mousedown="handleMouseDown"
      @mousemove="throttledHandleMouseMove"
      @mouseup="handleMouseUp"
      @touchstart="debouncedHandleTouchStart"
      @touchmove="throttledHandleTouchMove"
      @touchend="handleTouchEnd"
    >
      <v-layer>
        <v-line
          v-for="line in penLines"
          :key="line.uid"
          :points="line.points"
          :stroke="line.color"
          :strokeWidth="
            line.type === 'HIGHLIGHT_PEN'
              ? line.strokeWidth
              : line.strokeWidth / 2
          "
          :tension="0.5"
          :dash="line.type === 'DOTTED_LINE' ? [10, line.strokeWidth] : []"
          :lineCap="line.type === 'HIGHLIGHT_PEN' ? 'round' : 'square'"
          :lineJoin="line.type === 'HIGHLIGHT_PEN' ? 'round' : 'bevel'"
          :globalCompositeOperation="
            line.type === 'ERASER' ? 'destination-out' : 'source-over'
          "
        />
      </v-layer>
    </v-stage>
  </div>
</template>

<script setup>
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from "vue";
import { storeToRefs } from "pinia";
import { useEventListener } from "vue-hooks-plus";
import { useElementSize, useThrottleFn, useDebounceFn } from "@vueuse/core";
import { useNotePenStore } from "@/stores/note-pen";

const props = defineProps({
  editEnabled: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    required: true,
  },
  lastUid: {
    type: Number,
    default: 0,
  },
  disableScroll: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["update-item", "delete-item"]);

const INITIAL_STAGE_WIDTH = 768;
const INITIAL_STAGE_HEIGHT = 1024;

const noteFreeDrawingRef = ref();
const divSize = useElementSize(noteFreeDrawingRef);
const notePenStore = useNotePenStore();
const { aliveTool, aliveColor, aliveLineWidth } = storeToRefs(notePenStore);

const drawingEnabled = computed(() => {
  return props.editEnabled;
});

const penContentHeight = computed(() => {
  const penLines = props.item.value ? props.item.value : [];
  if (!penLines || penLines?.length === 0) {
    return 0;
  }

  if (drawingEnabled.value && props.item.uid === props.lastUid) {
    return 0;
  }

  const maxY = penLines.reduce((acc, cur) => {
    const curMaxY = Math.max(...cur.points.filter((_, idx) => idx % 2 === 1));
    return Math.max(acc, curMaxY);
  }, 0);

  return maxY + 20;
});

const widthScale = computed(() => {
  return divSize?.width?.value ? divSize.width.value / INITIAL_STAGE_WIDTH : 1;
});

const stageConfig = computed(() => {
  return {
    width: divSize?.width?.value ? divSize.width.value : INITIAL_STAGE_WIDTH,
    height:
      penContentHeight.value > 0
        ? penContentHeight.value * widthScale.value
        : INITIAL_STAGE_HEIGHT,
    scaleX: widthScale.value,
    scaleY: widthScale.value,
  };
});

const activeStyle = computed(() => {
  return {
    border: drawingEnabled.value
      ? "1px solid #409eff"
      : "1px solid transparent",
  };
});

const penLines = ref(props.item.value ?? []);
let isDrawing = false;

watch(
  () => props.item.value?.length,
  () => {
    penLines.value = props.item.value ?? [];
  }
);

watch(
  () => drawingEnabled.value,
  (newVal, oldValue) => {
    if (oldValue && !newVal) {
      if (penLines.value.length === 0) {
        emits("delete-item", props.item);
      } else {
        truncatePenLines();
        emits("update-item", { ...props.item, value: penLines.value });
      }
    }
  }
);

onBeforeUnmount(() => {
  if (penLines.value.length === 0) {
    emits("delete-item", props.item);
  } else {
    truncatePenLines();
    emits("update-item", { ...props.item, value: penLines.value });
  }
});

onMounted(() => {
  if (
    props.editEnabled &&
    props.item &&
    (!props.item.value || props.item.value.length === 0)
  ) {
    nextTick(() => {
      scrollToComponent();
    });
  }
});

function scrollToComponent() {
  if (props.disableScroll) {
    return;
  }

  if (noteFreeDrawingRef.value) {
    noteFreeDrawingRef.value?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }
}

function handleMouseDown(e) {
  if (!drawingEnabled.value) {
    return;
  }
  if (e.evt.cancelable) e.evt.preventDefault();

  isDrawing = true;
  const pos = e.target?.getStage()?.getPointerPosition();
  if (!pos) {
    return;
  }

  // 按比例缩放
  pos.x = pos.x / widthScale.value;
  pos.y = pos.y / widthScale.value;

  let color = aliveColor.value;
  if (color.startsWith("rgb")) {
    // 荧光笔的alpha值为0.3
    if (aliveTool.value === "HIGHLIGHT_PEN") {
      color = color.replace("rgb", "rgba").replace(")", ", 0.3)");
    } else {
      color = color.replace("rgb", "rgba").replace(")", ", 1)");
    }
  }

  let strokeWidth = aliveLineWidth.value;
  if (aliveTool.value === "ERASER") {
    strokeWidth = aliveLineWidth.value * 5;
  } else if (
    aliveTool.value === "HIGHLIGHT_PEN" ||
    aliveTool.value === "HIGHLIGHT_PEN_STRAIGHT"
  ) {
    strokeWidth = aliveLineWidth.value;
  } else {
    strokeWidth = aliveLineWidth.value / 2;
  }

  const newLine = {
    uid: Date.now(),
    type: aliveTool.value,
    color: color,
    strokeWidth: strokeWidth,
    points: [pos.x, pos.y],
  };
  penLines.value = [...penLines.value, newLine];
}

const throttledHandleMouseMove = useThrottleFn(handleMouseMove, 16); // ~60fps

function handleMouseMove(e) {
  // no drawing - skipping
  if (!isDrawing || !drawingEnabled.value) {
    return;
  }
  if (e.evt.cancelable) e.evt.preventDefault();

  const stage = e.target.getStage();
  const point = stage?.getPointerPosition();
  const lastLine = penLines.value[penLines.value.length - 1];
  if (!lastLine || !point) {
    return;
  }

  // 按比例缩放
  point.x = point.x / widthScale.value;
  point.y = point.y / widthScale.value;

  if (aliveTool.value === "LINE" || aliveTool.value === "DOTTED_LINE") {
    // 直线笔时，只保留前两个点
    lastLine.points = [
      lastLine.points[0] ?? 0,
      lastLine.points[1] ?? 0,
      point.x,
      point.y,
    ];
  } else {
    // add point
    lastLine.points = lastLine.points.concat([point.x, point.y]);
  }

  // replace last
  penLines.value = penLines.value
    .slice(0, penLines.value.length - 1)
    .concat(lastLine);
}

function handleMouseUp() {
  isDrawing = false;
}

const debouncedHandleTouchStart = useDebounceFn(handleTouchStart, 0);

function handleTouchStart(e) {
  if (!drawingEnabled.value) {
    return;
  }

  if (e.evt.touches.length === 1) {
    handleMouseDown(e);
  }
}

const throttledHandleTouchMove = useThrottleFn(handleTouchMove, 16);

function handleTouchMove(e) {
  if (e.evt.touches.length === 1) {
    handleMouseMove(e);
  }
}

function handleTouchEnd() {
  handleMouseUp();
}

useEventListener("mouseup", handleMouseUp, { target: window });
useEventListener("touchend", handleTouchEnd, { target: window });

// 对笔记进行上下裁剪，上边距保留25px
function truncatePenLines() {
  if (penLines.value.length === 0) {
    return;
  }

  const minY = penLines.value.reduce((acc, cur) => {
    const curMinY = Math.min(...cur.points.filter((_, idx) => idx % 2 === 1));
    return Math.min(acc, curMinY);
  }, Infinity);

  if (minY > 25) {
    penLines.value = penLines.value.map((line) => {
      return {
        ...line,
        points: line.points.map((point, idx) => {
          if (idx % 2 === 1) {
            return point - minY + 25;
          }
          return point;
        }),
      };
    });
  }
}
</script>

<style scoped></style>
