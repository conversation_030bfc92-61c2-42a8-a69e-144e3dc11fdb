<template>
  <van-card
    style="margin-bottom: 10px; margin-top: 10px"
    :desc="file.lastModified"
    :title="file.name"
    :thumb="file.image"
    :tag="file.notesCount"
    @click="OpenFile($event)"
  >
    <template #thumb>
      <div class="image-container">
        <img :src="file.image" class="image" />
      </div>
    </template>
    <template #tags>
      <van-tag plain type="primary" style="margin-top: 5px">PDF</van-tag>
    </template>
    <template #num>
      <van-popover
        v-model:show="showPopover"
        :actions="actions"
        placement="bottom-end"
        @select="onSelect"
      >
        <template #reference>
          <van-icon name="more-o" size="1.3rem" style="width: 30px" />
        </template>
      </van-popover>
    </template>
  </van-card>
</template>

<script setup>
import { toRefs, ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const props = defineProps({
  file: {
    type: Object,
    required: true,
  },
});
const { file } = toRefs(props);
const showPopover = ref(false);
const actions = [
  // {id: "share", text: "分享", icon: "share-o"},
  // {id: "export", text: "导出", icon: "description-o"},
  { id: "rename", text: "重命名", icon: "edit" },
  { id: "move", text: "移动", icon: "exchange" },
  { id: "delete", text: "删除", icon: "delete-o" },
  // {id: "statistics", text: "统计", icon: "info-o"},
];
const onSelect = (action) => {
  if (action.id === "statistics") {
    console.log("本文件笔记统计");
    router.push("/statisticsPage");
  }
};
const OpenFile = (event) => {
  if (event.target.classList.contains("van-icon")) {
    return;
  }
  console.log("打开笔记文件");
};
</script>
<style scoped>
.image-container {
  width: 100%;
  height: 100%;
}

.image {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
</style>
