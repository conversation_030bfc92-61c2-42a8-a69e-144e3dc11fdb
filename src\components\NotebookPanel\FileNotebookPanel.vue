<template>
  <div class="container" v-loading="loading">
    <div class="container-header">
      <div class="header">
        <slot name="back">
          <el-button link type="primary">{{ "< 返回" }}</el-button>
        </slot>
        <h3 class="title">笔记</h3>
        <div></div>
      </div>
      <div class="tags">
        <el-scrollbar>
          <div class="scrollbar-flex-content">
            <el-button
              v-for="t in tags"
              :key="t"
              :type="selectedTag === t ? 'success' : 'primary'"
              plain
              round
              class="scrollbar-flex-item"
              @click="selectTag(t)"
            >
              {{ t }}
            </el-button>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="container-body">
      <div v-if="showingNotes.length === 0">
        <div style="text-align: center">
          <img src="@/assets/image/cactus.png" />
          <h3>暂无笔记内容</h3>
        </div>
      </div>
      <el-scrollbar>
        <el-row>
          <el-col
            v-for="note in showingNotes"
            :key="note.uid"
            :xs="24"
            :sm="12"
            :lg="8"
          >
            <div class="box-card">
              <FileNoteCard :rect="note.rect" :note="note" />
            </div>
          </el-col>
        </el-row>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
// @ts-expect-error import js lib
import { useAppStore } from "@/stores/app";
// @ts-expect-error import js lib
import { listFileRects, getNoteFromFile } from "@/lib/RectDatabase.js";
import FileNoteCard from "@/components/NotebookPanel/FileNoteCard.vue";

type RECT = { uid: number };
type NOTE = { uid: number; keyword: string; rect: RECT };

const appStore = useAppStore();
const loading = ref(false);
const rects = ref<RECT[]>([]);
const notes = ref<NOTE[]>([]);
const tags = ref<string[]>([]);
const selectedTag = ref<string>("");

onMounted(() => {
  init();
});

async function init() {
  loading.value = true;
  rects.value = await listFileRects(appStore.currentFileId);
  const rectsIds = rects.value.map((rect) => rect.uid);
  const rawNotes = await getNoteFromFile(appStore.currentFileId);
  notes.value = rawNotes
    .filter((note: { uid: number }) => rectsIds.includes(note.uid))
    .map((note: { uid: number; keyword: string }) => ({
      ...note,
      rect: rects.value.find((rect) => rect.uid === note.uid),
    }));
  const keywords = notes.value
    .map((note) => note.keyword)
    .filter((keyword) => keyword);
  tags.value = [...new Set(keywords.flat())];
  loading.value = false;
}

function selectTag(tag: string) {
  if (selectedTag.value === tag) {
    selectedTag.value = "";
  } else {
    selectedTag.value = tag;
  }
}

const showingNotes = computed(() => {
  if (selectedTag.value) {
    return notes.value.filter((note) => note.keyword === selectedTag.value);
  }
  return notes.value;
});
</script>

<style lang="scss" scoped>
.container {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100vh;

  .container-header {
    display: grid;
    grid-template-rows: auto auto;
  }

  @media screen and (orientation: landscape) and (max-width: 992px) {
    grid-template-rows: 1fr;
    grid-template-columns: 150px 1fr;

    .container-header {
      display: grid;
      grid-template-rows: auto 1fr;
      height: 100vh;
    }
  }
}

.header {
  padding: 10px;
  display: grid;
  grid-template-columns: 60px 1fr 60px;
  align-items: center;

  .title {
    text-align: center;
  }

  @media screen and (orientation: landscape) and (max-width: 992px) {
    .title {
      display: none;
    }
  }
}

.tags {
  padding: 5px;
  overflow: hidden;

  .scrollbar-flex-content {
    display: flex;
  }

  .scrollbar-flex-item {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
  }

  @media screen and (orientation: landscape) and (max-width: 992px) {
    .scrollbar-flex-content {
      flex-direction: column;
    }

    .scrollbar-flex-item {
      margin: 5px 0;
    }
  }
}

.container-body {
  padding: 5px;
  overflow: hidden;

  .box-card {
    margin: 5px;
    padding: 0 10px;
    background-color: #fdfaf3;
  }
}
</style>
