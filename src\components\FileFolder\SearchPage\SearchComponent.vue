<template>
    <div>
        <van-search v-if="tempSearchType === 'file'" :value="tempKeyWord" show-action placeholder="按名称搜索文档" @search="onSearch"
            @change="onSearch">
            <template #action>
                <div style="text-align: center;">
                    <el-button-group>
                        <el-button type="primary" @click="updateSearchType('file')">文档</el-button>
                        <el-button type="primary" plain @click="updateSearchType('note')">笔记</el-button>
                    </el-button-group>
                </div>
            </template>
        </van-search>
        <van-search v-if="tempSearchType === 'note'" :value="tempKeyWord" show-action placeholder="按关键字搜索笔记" @search="onSearch"
            @change="onSearch">
            <template #action>
                <div style="text-align: center;">
                    <el-button-group>
                        <el-button type="primary" plain @click="updateSearchType('file')">文档</el-button>
                        <el-button type="primary" @click="updateSearchType('note')">笔记</el-button>
                    </el-button-group>
                </div>
            </template>
        </van-search>
    </div>
</template>

<script setup>
import { ref } from 'vue';


const props = defineProps({
    searchType: String,
    keyWord: String
});
const emit = defineEmits(['onSearch']);
const tempSearchType = ref(props.searchType)
const tempKeyWord = ref(props.keyWord)

const updateSearchType = (type) => {
    tempSearchType.value = type;
    emit('onSearch', { searchType: tempSearchType, keyWord: tempKeyWord.value });
};

const onSearch = () => {
    emit('onSearch', { searchType: tempSearchType, keyWord: tempKeyWord.value });
};


</script>