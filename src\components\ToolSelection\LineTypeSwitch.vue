<template>
  <div v-if="showLineType">
    <h4 style="margin-top: 15px">线型：</h4>
    <van-radio-group
      v-model="checked"
      direction="horizontal"
      @change="changeLineType"
    >
      <van-radio name="PEN" class="radio-style">自由书写</van-radio>
      <van-radio name="LINE" class="radio-style">直线</van-radio>
      <van-radio name="DOTTED_LINE" class="radio-style">虚直线</van-radio>
    </van-radio-group>
  </div>
  <div v-else-if="showLineTypeHighlightPen">
    <h4 style="margin-top: 15px">线型：</h4>
    <van-radio-group
      v-model="checkedHighlightPen"
      direction="horizontal"
      @change="changeLineType"
    >
      <van-radio name="HIGHLIGHT_PEN" class="radio-style">自由书写</van-radio>
      <van-radio name="HIGHLIGHT_PEN_STRAIGHT" class="radio-style">
        直线
      </van-radio>
    </van-radio-group>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
const props = defineProps({
  currentTool: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["change-line-type"]);

const checked = ref("");
const checkedHighlightPen = ref("");
const showLineType = ref(false);
const showLineTypeHighlightPen = ref(false);

onMounted(() => {
  checked.value = props.currentTool.type;
  //EMPTY:0无 HAND:1手型 PEN:2笔型 BOX_SELECT:3框选 ADD_TEXT:4添加文字 ADD_IMAGE:5图片 TEXT_POP_UP:6文字上方弹窗 HIGHLIGHT_PEN：7荧光笔 LINE：8直线 DOTTED_LINE：9虚线 ERASER：10橡皮擦
  if (
    props.currentTool.type === "PEN" ||
    props.currentTool.type === "LINE" ||
    props.currentTool.type === "DOTTED_LINE"
  ) {
    showLineType.value = true;
  }

  checkedHighlightPen.value = props.currentTool.type;
  if (
    props.currentTool.type === "HIGHLIGHT_PEN" ||
    props.currentTool.type === "HIGHLIGHT_PEN_STRAIGHT"
  ) {
    showLineTypeHighlightPen.value = true;
  }
});

const changeLineType = (newValue) => {
  emit("change-line-type", newValue);
};
</script>
<style scoped>
.radio-style {
  margin-top: 10px;
}
</style>
