<template>
  <div style="margin-left: 10px; margin-right: 10px">
    <div class="bottom-actions">
      <div style="margin-left: auto">
        <el-button text type="primary" @click="toggleFullscreen">
          <i
            v-if="!fullscreen"
            class="bi bi-fullscreen"
            style="font-size: 20px"
          ></i>
          <i v-else class="bi bi-fullscreen-exit" style="font-size: 20px"></i>
        </el-button>
        <NoteCardBottom @delete-note="deleteNote" />
      </div>
    </div>
  </div>
</template>

<script setup>
import NoteCardBottom from "@/components/NotebookPanel/NoteCardCompt/NoteCardBottom.vue";

defineProps({
  fullscreen: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["delete-note", "toggle-fullscreen"]);

function toggleFullscreen() {
  emits("toggle-fullscreen");
}

function deleteNote() {
  emits("delete-note");
}
</script>

<style scoped>
.bottom-actions {
  /* margin-top: 15px; */
  width: 100%;
  display: flex;
  align-items: center;
}
</style>
