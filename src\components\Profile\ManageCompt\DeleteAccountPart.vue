<template>
  <div class="parent" @click="btnShowDelDialog">
    <el-text>注销我的账号</el-text>
    <div class="content">
      <el-text type="info">注销账号</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
  <el-dialog
    v-model="showDeleteDialog"
    title="注销账号"
    :append-to-body="true"
    class="account-delete-confirm-dialog"
  >
    <h2>重要提醒</h2>
    <h3>1、永久注销，无法登陆</h3>
    <el-text type="info">
      一旦注销成功，你的账号将无法登陆使用，请慎重！在注销账户后，我们将停止为您提供产品或服务。如果您有任何疑问、意见、建议与客服联系，我们会给予您必要的帮助。
    </el-text>
    <h3>2、所有数据将无法找回</h3>
    <el-text type="info">
      注销您的账户将导致您永久失去对账户和账户中数据的访问权。请妥善保存您的数据。
    </el-text>
    <h3>
      3、提交注销之后，我们将删除账号，在15天内，完成对账号中内容的删除和匿名化处理。
    </h3>
    <el-text type="danger" tag="p">
      请按下方的“申请注销”按钮，即表示您已阅读并同意
    </el-text>
    <template #footer>
      <span>
        <el-button @click="cancelDelete">取消</el-button>
        <el-button
          type="danger"
          :loading="loading"
          :disabled="preventMistakeCountdown > 0"
          @click="confirmDelete"
        >
          <span v-if="preventMistakeCountdown" style="margin-right: 5px">
            {{ preventMistakeCountdown }}
          </span>
          确认注销
        </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="showCodeDialog"
    :fullscreen="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :show-close="false"
  >
    <SmsCodeDialog
      :phone="phonePlaceHolder"
      :pre-count-down="preCountDown"
      :confirm-button-text="'注销账号'"
      :parent-loading="loading || delLoading"
      :intercept-code-resend="true"
      @do-code-action="doDelRequest"
      @do-code-resend="confirmDelete"
      @close-dialog="cancelDelete"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, ref } from "vue";
import { ElMessage } from "element-plus";
import SmsCodeDialog from "@/components/Profile/LoginPage/SmsCodeDialog.vue";
import {
  fetchUserDeleteAuth,
  fetchUserDeleteConfirm,
} from "@/components/Profile/Requests/FetchUserAPI";

const emits = defineEmits(["logout"]);

const preventMistakeCountdown = ref(0);
const preventMistakeCountdownTimer = ref();
const showDeleteDialog = ref(false);
const showCodeDialog = ref(false);
const loading = ref(false);
const phonePlaceHolder = ref("");
const preCountDown = ref(0);
const delLoading = ref(false);

function btnShowDelDialog() {
  showDeleteDialog.value = true;

  if (preventMistakeCountdownTimer.value) {
    clearInterval(preventMistakeCountdownTimer.value);
  }

  preventMistakeCountdown.value = 15;
  preventMistakeCountdownTimer.value = setInterval(() => {
    preventMistakeCountdown.value -= 1;
    if (preventMistakeCountdown.value <= 0) {
      clearInterval(preventMistakeCountdownTimer.value);
    }
  }, 1000);
}

function cancelDelete() {
  showDeleteDialog.value = false;
  showCodeDialog.value = false;
  preCountDown.value = 0;
  phonePlaceHolder.value = "";
  loading.value = false;
  delLoading.value = false;

  if (preventMistakeCountdownTimer.value) {
    clearInterval(preventMistakeCountdownTimer.value);
  }
  preventMistakeCountdown.value = 0;
}

function confirmDelete() {
  // 执行注销账号的操作
  loading.value = true;
  fetchUserDeleteAuth()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("验证码已发送，请注意查收");
        showDeleteDialog.value = false;
        showCodeDialog.value = true;
        preCountDown.value = 0;
        phonePlaceHolder.value = data.data?.phone ?? "";
      } else if (data.resCode && data.resCode === "HAS_SEND_CODE") {
        ElMessage.error(data.message);
        showDeleteDialog.value = false;
        showCodeDialog.value = true;
        preCountDown.value = data.data?.countDown ?? 0;
        phonePlaceHolder.value = data.data?.phone ?? "";
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("发送验证码失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("发送验证码失败，请稍后重试");
    })
    .finally(() => {
      loading.value = false;
    });
}

function doDelRequest(_phone: string, code: string) {
  delLoading.value = true;
  fetchUserDeleteConfirm(code)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("注销成功");
        cancelDelete();
        emits("logout");
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("注销失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("注销失败，请稍后重试");
    })
    .finally(() => {
      delLoading.value = false;
    });
}

onBeforeUnmount(() => {
  if (preventMistakeCountdownTimer.value) {
    clearInterval(preventMistakeCountdownTimer.value);
  }
});
</script>

<style lang="scss" scoped>
div {
  &.parent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  &.content {
    display: flex;
    align-items: center;

    & > * {
      margin-left: 5px;
    }
  }
}

:global(.account-delete-confirm-dialog) {
  width: 768px;

  @media screen and (max-width: 768px) {
    width: 80%;
  }
}
</style>
