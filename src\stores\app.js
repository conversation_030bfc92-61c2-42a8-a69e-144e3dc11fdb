import { defineStore } from "pinia";
import { computed, ref } from "vue";

export const useAppStore = defineStore("app", () => {
  const globalRouterRefreshKey = ref(0);
  const deviceType = ref("web");
  const appVersion = ref("1.0.0");
  const currentFileMeta = ref(null);
  const currentFolderMeta = ref(null);
  const currentFile = ref(null);
  const showIframeCover = ref(false);
  const currentSearchingRect = ref(null);
  const lastVisitedFolderId = ref(0);

  const currentFileId = computed(() => currentFileMeta.value?.id ?? "");
  const currentFolderId = computed(() => currentFolderMeta.value?.id ?? "");
  const currentFileName = computed(() => currentFileMeta.value?.name ?? "");

  const isMobile = computed(() => deviceType.value === "mobile");

  // breakpoint: 1.0.3版本之前插入图片用`save-file-data-to-storage`，之后用`insert-file-data-to-storage`
  const insertFileDataToStorage = computed(() => {
    return appVersion.value >= "1.0.3";
  });

  return {
    globalRouterRefreshKey,
    deviceType,
    isMobile,
    appVersion,
    currentFileMeta,
    currentFolderMeta,
    currentFileId,
    currentFolderId,
    currentFileName,
    currentFile,
    showIframeCover,
    currentSearchingRect,
    lastVisitedFolderId,
    insertFileDataToStorage,
  };
});
