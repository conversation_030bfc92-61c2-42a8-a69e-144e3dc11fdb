<template>
  <div>
    <van-search
      v-model="tempKeyWord"
      show-action
      :disabled="disabled"
      placeholder="按标签、选中内容搜索"
      @search="onSearch"
      @clear="onClear"
    >
      <template #action>
        <div style="text-align: center">
          <el-button type="primary" plain @click="showDrawer = true"
            >标签</el-button
          >
          <el-button type="primary" @click="onSearch">搜索</el-button>
        </div>
      </template>
    </van-search>
    <el-drawer
      v-model="showDrawer"
      title="笔记标签"
      :append-to-body="true"
      :size="isVertical ? '70%' : drawerLandscapeWidth"
      :direction="isVertical ? 'btt' : 'rtl'"
    >
      <NoteCardTagDrawer @select="handleSelect" />
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { useWindowSize } from "@vueuse/core";
import NoteCardTagDrawer from "@/components/CenterDialogNote/NoteCard/NoteCardTagDrawer.vue";

defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["onSearch"]);
const tempKeyWord = ref("");
const showDrawer = ref(false);

const { width, height } = useWindowSize();
const isVertical = computed(() => width.value > height.value);
const drawerLandscapeWidth = computed(() =>
  width.value < 900 ? "300px" : "30%"
);

function handleSelect(text) {
  tempKeyWord.value = text;
  showDrawer.value = false;
  onSearch();
}

function onSearch() {
  emit("onSearch", tempKeyWord.value);
}

function onClear() {
  tempKeyWord.value = "";
  onSearch();
}
</script>
