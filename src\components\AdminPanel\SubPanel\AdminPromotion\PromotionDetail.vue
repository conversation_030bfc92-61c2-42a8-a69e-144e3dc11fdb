<template>
  <el-card v-loading="loading">
    <el-form
      ref="formRef"
      :model="promotion"
      :rules="rules"
      label-width="120px"
      status-icon
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="promotion.name" />
      </el-form-item>
      <el-form-item label="折扣类型" prop="discountType">
        <el-select
          v-model="promotion.discountType"
          placeholder="请选择折扣类型"
        >
          <el-option label="百分比" value="PERCENTAGE" />
          <el-option label="固定值" value="AMOUNT" />
        </el-select>
      </el-form-item>
      <el-form-item label="折扣大小" prop="discountValue">
        <el-input-number v-model="promotion.discountValue" :min="0" />
      </el-form-item>
      <el-form-item label="最大折扣金额" prop="maxDiscountValue">
        <el-input-number v-model="promotion.maxDiscountValue" :min="0" />
      </el-form-item>
      <el-form-item label="限定新用户" prop="newCustomerOnly">
        <el-switch v-model="promotion.newCustomerOnly" />
      </el-form-item>
      <el-form-item label="限定新订单" prop="onlyFirstOrder">
        <el-switch v-model="promotion.onlyFirstOrder" />
      </el-form-item>
      <el-form-item label="有效日期" prop="endDate">
        <el-date-picker
          v-model="availableDate"
          type="daterange"
          range-separator="至"
          start-placeholder="请选择开始时间"
          end-placeholder="请选择结束时间"
        />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input type="textarea" v-model="promotion.description" />
      </el-form-item>
      <el-form-item label="可用套餐" prop="applicableLevels">
        <el-select
          v-model="promotion.applicableLevels"
          multiple
          placeholder="请选择可用套餐"
        >
          <el-option
            v-for="level in levels"
            :key="level.id"
            :label="level.name"
            :value="level.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button v-if="props.id" type="danger" @click="deletePromotion">
          删除
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import {
  fetchAddPromotionFormData,
  fetchPromotionDetails,
  fetchDeletePromotion,
  fetchAddPromotion,
  fetchUpdatePromotion,
} from "@/components/AdminPanel/SubPanel/FetchAdminAPI";

const props = defineProps({
  id: {
    type: String,
    required: false,
  },
});

const emits = defineEmits(["update", "delete"]);

type Level = {
  id: string;
  name: string;
};

type Promotion = {
  id?: string;
  name: string;
  discountType: string;
  discountValue: number;
  maxDiscountValue: number;
  newCustomerOnly: boolean;
  onlyFirstOrder: boolean;
  startDate: string;
  endDate: string;
  description: string;
  applicableLevels: string[];
};

const rules = reactive<FormRules<Promotion>>({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  discountType: [
    { required: true, message: "请选择折扣类型", trigger: "blur" },
    {
      type: "enum",
      enum: ["PERCENTAGE", "AMOUNT"],
      message: "请选择正确的折扣类型",
      trigger: "blur",
    },
  ],
  discountValue: [
    { required: true, message: "请输入折扣大小", trigger: "blur" },
    { type: "number", message: "请输入数字", trigger: "blur" },
    {
      validator: (_, value) => value > 0,
      message: "请输入大于0的数字",
      trigger: "blur",
    },
  ],
  maxDiscountValue: [
    { required: true, message: "请输入最大折扣金额", trigger: "blur" },
    { type: "number", message: "请输入数字", trigger: "blur" },
  ],
  newCustomerOnly: [
    { required: true, message: "请选择是否限定新用户", trigger: "blur" },
  ],
  onlyFirstOrder: [
    { required: true, message: "请选择是否限定新订单", trigger: "blur" },
  ],
  startDate: [{ required: true, message: "请选择正确的时间", trigger: "blur" }],
  endDate: [
    { required: true, message: "请选择正确的时间", trigger: "blur" },
    {
      validator: (_, value) => value > promotion.startDate,
      message: "结束时间必须大于开始时间",
      trigger: "blur",
    },
  ],
  applicableLevels: [
    { required: true, message: "请选择可用套餐", trigger: "blur" },
    {
      validator: (_, value) =>
        value.length > 0 &&
        value.every((item: string) =>
          levels.value.some((level) => level.id === item)
        ),
      message: "请选择正确的套餐",
      trigger: "blur",
    },
  ],
});

const loading = ref(false);
const formRef = ref<FormInstance>();
const defaultPromotion = ref<Promotion>({
  name: "",
  discountType: "",
  discountValue: 0,
  maxDiscountValue: 0,
  newCustomerOnly: false,
  onlyFirstOrder: false,
  startDate: "",
  endDate: "",
  description: "",
  applicableLevels: [],
});
const promotion = reactive<Promotion>({ ...defaultPromotion.value });
const levels = ref<Level[]>([]);
const availableDate = ref<string[]>([]);

watch(availableDate, (newVal) => {
  if (newVal?.length === 2) {
    promotion.startDate = newVal[0];
    promotion.endDate = newVal[1];
  } else {
    promotion.startDate = "";
    promotion.endDate = "";
  }
});

onMounted(() => {
  loading.value = true;
  fetchAddPromotionFormData()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        const levelData = data.data?.levels;
        if (levelData) {
          levels.value = levelData;
        }
        if (props.id) {
          return fetchPromotionDetails(props.id);
        } else {
          return Promise.resolve({});
        }
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        const promotionData = data.data?.promotion;
        if (promotionData) {
          updatePromotion(promotionData);
        }
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      loading.value = false;
    });
});

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (props.id) {
        fetchUpdatePromotion(props.id, promotion)
          .then((data) => {
            if (data.resCode && data.resCode === "SUCCESS") {
              const promotionData = data.data?.promotion;
              if (promotionData) {
                updatePromotion(promotionData);
              }

              ElMessage.success("更新成功");
              emits("update");
            } else if (data.message) {
              ElMessage.error(data.message);
            }
          })
          .catch(() => {
            ElMessage.error("更新失败");
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        fetchAddPromotion(promotion)
          .then((data) => {
            if (data.resCode && data.resCode === "SUCCESS") {
              const promotionData = data.data?.promotion;
              if (promotionData) {
                updatePromotion(promotionData);
              }

              ElMessage.success("添加成功");
              emits("update");
            } else if (data.message) {
              ElMessage.error(data.message);
            }
          })
          .catch(() => {
            ElMessage.error("添加失败");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    } else {
      ElMessage.error("表单验证失败");
    }
  });
};

function updatePromotion(
  promotionData: Promotion & { applicableLevels: Level[] }
) {
  Object.assign(promotion, {
    ...promotionData,
    discountValue: Number(promotionData.discountValue),
    maxDiscountValue: Number(promotionData.maxDiscountValue),
    applicableLevels: promotionData.applicableLevels.map(
      (level: Level) => level.id
    ),
  });
  defaultPromotion.value = {
    ...promotionData,
    discountValue: Number(promotionData.discountValue),
    maxDiscountValue: Number(promotionData.maxDiscountValue),
    applicableLevels: promotionData.applicableLevels.map(
      (level: Level) => level.id
    ),
  };
  availableDate.value = [promotionData.startDate, promotionData.endDate];
}

const resetForm = () => {
  if (!formRef.value) return;
  Object.assign(promotion, { ...defaultPromotion.value });
  formRef.value.clearValidate();
};

function deletePromotion() {
  if (!props.id) return;
  loading.value = true;
  fetchDeletePromotion(props.id)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("删除成功");
        emits("delete");
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("删除失败");
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
