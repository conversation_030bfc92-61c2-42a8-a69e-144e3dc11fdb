<template>
  <el-main>
    <VersionInfo :show-back="true" title="设置" />
    <el-row justify="center">
      <el-col :sm="16" :lg="12">
        <el-card shadow="never">
          <el-row v-if="userProfile">
            <el-col>
              <UsernamePart />
              <el-divider style="margin: 12px 0" />
            </el-col>
          </el-row>
          <el-row v-if="userProfile">
            <el-col>
              <LoginManagePart />
              <el-divider style="margin: 12px 0" />
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <OcrEngine />
              <el-divider style="margin: 12px 0" />
            </el-col>
          </el-row>
          <el-row v-if="userProfile">
            <el-col>
              <DeleteAccount @logout="logout" />
              <el-divider style="margin: 12px 0" />
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <PrivacyPolicy />
              <el-divider style="margin: 12px 0" />
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <UserAgreement />
            </el-col>
          </el-row>
          <el-row v-if="userProfile">
            <el-col>
              <el-divider style="margin: 12px 0" />
              <WithdrawEULA @logout="logout" />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <el-row v-if="userProfile?.isAdmin" justify="center" style="margin-top: 15px">
      <el-col :sm="16" :lg="12">
        <el-button type="success" style="width: 100%" @click="gotoAdminPanel">
          管理面板
        </el-button>
      </el-col>
    </el-row>
    <el-row v-if="userProfile" justify="center" style="margin-top: 15px">
      <el-col :sm="16" :lg="12">
        <Logout @logout="logout" />
      </el-col>
    </el-row>
    <div style="margin-bottom: 50px"></div>
    <BottomTabBar active="profile" />
  </el-main>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useProfileStore } from "@/stores/profile";
import VersionInfo from "@/components/Profile/VersionInfo.vue";
import UsernamePart from "@/components/Profile/ManageCompt/UsernamePart.vue";
import LoginManagePart from "@/components/Profile/ManageCompt/LoginManagePart.vue";
import OcrEngine from "@/components/Profile/ManageCompt/OcrEngine.vue";
import DeleteAccount from "@/components/Profile/ManageCompt/DeleteAccountPart.vue";
import PrivacyPolicy from "@/components/Profile/ManageCompt/PrivacyPolicyPart.vue";
import UserAgreement from "@/components/Profile/ManageCompt/UserAgreementPart.vue";
import WithdrawEULA from "@/components/Profile/ManageCompt/WithdrawEULAPart.vue";
import Logout from "@/components/Profile/ManageCompt/LogoutPart.vue";
// @ts-expect-error 导入js组件
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";

const router = useRouter();
const profileStore = useProfileStore();
const userProfile = computed(() => profileStore.userProfile);

function gotoAdminPanel() {
  router.push("/admin");
}

function logout() {
  profileStore.userProfile = null;
  router.back();
}
</script>
