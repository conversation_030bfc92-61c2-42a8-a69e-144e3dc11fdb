<template>
  <router-view :key="appStore.globalRouterRefreshKey"></router-view>
</template>

<script setup>
import { watch } from "vue";
import { liveQuery } from "dexie";
import { useObservable } from "@vueuse/rxjs";
import { useAppStore } from "@/stores/app";
import { useFileFolderStore } from "@/stores/file-folder";
import { getFileMetaList } from "@/lib/FileList.js";

const appStore = useAppStore();
const fileFolderStore = useFileFolderStore();
const fileList = useObservable(liveQuery(() => getFileMetaList()));

watch(fileList, () => {
  const list = fileList.value ?? [];

  fileFolderStore.fileList = list.sort((a, b) => {
    if (a.type === "folder" && b.type !== "folder") {
      return -1;
    } else if (b.type === "folder" && a.type !== "folder") {
      return 1;
    } else {
      if (fileFolderStore.sortType === "name") {
        return a.name.localeCompare(b.name);
      } else if (fileFolderStore.sortType === "size") {
        return a.size - b.size;
      } else if (fileFolderStore.sortType === "date") {
        return Number(b.id) - Number(a.id);
      } else {
        return 0;
      }
    }
  });

  fileFolderStore.firstFileListQueried = true;
});
</script>
