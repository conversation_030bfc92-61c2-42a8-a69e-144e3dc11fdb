<template>
  <el-main>
    <VersionInfo
      :show-back="true"
      title="登录"
      :emit-back="true"
      @back="goBack"
    />
    <div class="login-card">
      <el-image :src="AppIcon" class="login-icon" />
      <div>
        <el-input
          v-model="phone"
          placeholder="请输入手机号"
          clearable
          :disabled="sendingCode"
          size="large"
        />
        <el-button
          :disabled="sendingCode || !phone"
          :loading="sendingCode"
          type="primary"
          size="large"
          style="width: 100%; margin-top: 10px"
          @click="sendCode"
        >
          获取验证码
        </el-button>
      </div>
      <!-- 用户协议和隐私政策 -->
      <div class="agree-checkbox">
        <el-checkbox v-model="agreed" style="margin-right: 5px" />
        <div style="display: flex; align-items: center; flex-wrap: wrap;">
          我已阅读并同意
          <el-link
            type="primary"
            :underline="false"
            style="margin: 0 5px"
            @click="showAgreement"
          >
            用户协议
          </el-link>
          和
          <el-link
            type="primary"
            :underline="false"
            style="margin: 0 5px"
            @click="showPrivacy"
          >
            隐私政策
          </el-link>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="showCodeDialog"
      :fullscreen="true"
      :append-to-body="true"
      :destroy-on-close="true"
      :show-close="false"
    >
      <SmsCodeDialog
        :phone="phone"
        :pre-count-down="countDown"
        :parent-loading="loginLoading"
        @do-code-action="doLogin"
        @close-dialog="closeDialog"
      />
    </el-dialog>
    <el-dialog
      v-model="showAgreementDialog"
      title="用户协议"
      top="50px"
      width="90%"
      :append-to-body="true"
      :show-close="true"
    >
      <UserAgreementText />
    </el-dialog>
    <el-dialog
      v-model="showPrivacyDialog"
      title="隐私政策"
      top="50px"
      width="90%"
      :append-to-body="true"
      :show-close="true"
    >
      <PrivacyPolicyText />
    </el-dialog>
    <div style="margin-bottom: 50px"></div>
    <BottomTabBar active="profile" />
  </el-main>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { ElLoading, ElMessage } from "element-plus";
import { useProfileStore } from "@/stores/profile";
import VersionInfo from "@/components/Profile/VersionInfo.vue";
import SmsCodeDialog from "@/components/Profile/LoginPage/SmsCodeDialog.vue";
// @ts-expect-error 导入js组件
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";
// @ts-expect-error todo: 待处理图片导入问题
import AppIcon from "@/assets/image/app_icon.png";
import UserAgreementText from "@/components/Profile/DocumentCompt/UserAgreementText.vue";
import PrivacyPolicyText from "@/components/Profile/DocumentCompt/PrivacyPolicyText.vue";
import {
  fetchUserProfile,
  fetchSendSmsCode,
  fetchUserLogin,
} from "@/components/Profile/Requests/FetchUserAPI";

const router = useRouter();
const profileStore = useProfileStore();
const phone = ref("");
const sendingCode = ref(false);
const agreed = ref(false);
const showCodeDialog = ref(false);
const countDown = ref(0);
const loginLoading = ref(false);
const showAgreementDialog = ref(false);
const showPrivacyDialog = ref(false);

const showAgreement = () => {
  showAgreementDialog.value = true;
};

const showPrivacy = () => {
  showPrivacyDialog.value = true;
};

function goBack() {
  const { from } = router.currentRoute.value.query;
  if (from && from === "member-info") {
    router.replace({
      path: "/profile/member-info",
      query: {
        from: "login",
      },
    });
  } else {
    router.back();
  }
}

function loginSuccess() {
  router.back();
}

onMounted(() => {
  const loading = ElLoading.service({
    lock: true,
  });

  fetchUserProfile()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        profileStore.userProfile = data.data;
        loginSuccess();
      }
    })
    .catch(() => {})
    .finally(() => {
      loading.close();
    });
});

const sendCode = () => {
  // 检查是否是有效手机号码
  if (!phone.value || !checkPhoneIsValid()) {
    ElMessage.error("请输入有效的手机号码");
    return;
  }

  if (!agreed.value) {
    ElMessage.error("请先阅读并同意用户协议和隐私政策");
    return;
  }

  sendCodeRequest();
};

function checkPhoneIsValid() {
  const phoneRegex = /^1[3456789]\d{9}$/;
  return phoneRegex.test(phone.value);
}

function sendCodeRequest() {
  sendingCode.value = true;
  fetchSendSmsCode(phone.value)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("验证码已发送，请注意查收");
        showCodeDialog.value = true;
        countDown.value = 0;
      } else if (data.resCode && data.resCode === "HAS_SEND_CODE") {
        ElMessage.error(data.message);
        showCodeDialog.value = true;
        countDown.value = data.data?.countDown ?? 0;
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("发送验证码失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("发送验证码失败，请稍后重试");
    })
    .finally(() => {
      sendingCode.value = false;
    });
}

function doLogin(phone: string, code: string) {
  loginLoading.value = true;
  fetchUserLogin(phone, code)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("登录成功");
        closeDialog();

        profileStore.userProfile = data.data;
        loginSuccess();
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("登录失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("登录失败，请稍后重试");
    })
    .finally(() => {
      loginLoading.value = false;
    });
}

function closeDialog() {
  showCodeDialog.value = false;
  countDown.value = 0;
}
</script>

<style lang="scss" scoped>
.login-card {
  max-width: 400px;
  margin: 30px auto;
}

.login-icon {
  width: 96px;
  height: 96px;
  display: block;
  margin: 0 auto 20px;

  @media screen and (max-width: 768px) {
    width: 64px;
    height: 64px;
  }
}

.agree-checkbox {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
