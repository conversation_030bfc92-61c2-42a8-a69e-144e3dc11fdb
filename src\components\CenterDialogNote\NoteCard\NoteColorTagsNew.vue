<template>
  <el-card
    class="tag-card"
    shadow="never"
    :bodyStyle="{
      padding: '5px 10px 5px 10px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    }"
  >
    <el-button link type="primary" @click="onAddTag">+ 新建标签</el-button>
  </el-card>
  <el-dialog
    v-model="editDialogVisible"
    title="新建标签"
    width="30%"
    :destroy-on-close="true"
    :style="{ minWidth: '350px' }"
  >
    <el-input
      class="tag-input"
      v-model="inputValue"
      placeholder="请输入标签名"
      clearable
      autofocus
    />
    <div style="margin-top: 10px; text-align: center">
      <el-button type="success" @click="onEditFinish">确定</el-button>
      <el-button type="danger" @click="onCancelEdit">取消</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";

const emits = defineEmits(["create-finish"]);

const editDialogVisible = ref(false);
const inputValue = ref("");

function onAddTag() {
  inputValue.value = "";
  editDialogVisible.value = true;
}

function onEditFinish() {
  editDialogVisible.value = false;
  emits("create-finish", { name: inputValue.value });
}

function onCancelEdit() {
  editDialogVisible.value = false;
}
</script>
<style scoped>
.tag-card {
  margin-bottom: 8px;
}

.no-border {
  border: none !important;
}

.card-flex {
  display: flex;
  justify-content: space-between;
}

.align-center-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.align-center-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.tag-input {
  width: 100%;
}
</style>
