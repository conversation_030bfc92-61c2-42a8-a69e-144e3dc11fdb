<template>
    <div style="margin-left: 10px;margin-right: 10px;margin-top: 15px;background-color: #f4f4f5;">
        <el-scrollbar style="padding: 5px;" :style="{ height: scrollbarHeight + 'px' }" ref="scrollbar">
            <van-cell v-for="note in noteList" :key="note.title" :title="note.title" :label="note.info" @click="openFile" style="margin-bottom: 5px;"/>
            <div style="margin-top: 10px;margin-bottom: 70px;">
                <h4 style="text-align: center;color: #409EFF;">加载更多</h4>
            </div>
        </el-scrollbar>
    </div>

</template>

<script setup>
import { ref, onMounted } from 'vue';

const scrollbar = ref(null);
const scrollbarHeight = ref(0);

onMounted(() => {
    const scrollbarY = scrollbar.value.$el.getBoundingClientRect().top;
    const viewportHeight = window.innerHeight;
    scrollbarHeight.value = viewportHeight - scrollbarY;
});

const openFile = () => {
    console.log('open file 打开笔记文件 并 跳转到笔记位置');
}

const noteList = [
    { "title": "Note 1","info":"笔记card文字", "color": "red" },
    { "title": "Note 2","info":"笔记card文字", "color": "blue" },
    { "title": "Note 3","info":"笔记card文字", "color": "green" },
    { "title": "Note 4","info":"笔记card文字", "color": "yellow" },
    { "title": "Note 5","info":"笔记card文字", "color": "purple" },
    { "title": "Note 6","info":"笔记card文字", "color": "orange" },
    { "title": "Note 7","info":"笔记card文字", "color": "black" },
    { "title": "Note 8","info":"笔记card文字", "color": "white" },
    { "title": "Note 9","info":"笔记card文字", "color": "white" },
    { "title": "Note 10","info":"笔记card文字", "color": "white" },
    { "title": "Note 11","info":"笔记card文字", "color": "white" },
    { "title": "Note 12","info":"笔记card文字", "color": "white" },
    { "title": "Note 13","info":"笔记card文字", "color": "white" },
    { "title": "Note 14","info":"笔记card文字", "color": "white" },
    { "title": "Note 15","info":"笔记card文字", "color": "white" },
    { "title": "Note 16","info":"笔记card文字", "color": "white" }

];
</script>

<style scoped>
.custom-title {
  color: #000000; 
  font-size: 15px; 
}
</style>