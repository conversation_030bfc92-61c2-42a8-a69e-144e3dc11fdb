<template>
  <div class="folder-tree-navigation-container">
    <FolderTreeNavigation :node-array="nodeArray"
                          @goBackOneLevel="onGoBackOneLevel"
                          @goRoot="onGoRoot"
    />
  </div>
  <div class="node-list-container">
    <CellGroup>
      <Cell v-for="node in fileNodes" :key="node.id"
            size="large"
            :title="node.name"
            :clickable="true"
            @click="enterNode(node)"
      >
        <template #icon>
          <el-icon :size="24" style="margin-right: 0.5rem">
            <Folder />
          </el-icon>
        </template>
      </Cell>
    </CellGroup>
  </div>
</template>

<script setup>
import {onMounted, ref, watch} from "vue";
import {CellGroup, Cell} from "vant";
import {ElIcon} from "element-plus";
import {Folder} from "@element-plus/icons-vue";
import {getFolderMetaList} from "@/lib/FileList.js";
import FolderTreeNavigation from "@/components/FileFolder/FolderTree/FolderTreeNavigation.vue";

const emits = defineEmits(["update-nodes"]);

const fileNodes = ref([]);
const nodeArray = ref([]);

onMounted(async () => {
  fileNodes.value = await getFolderMetaList("");
});

watch(
    () => nodeArray.value.length,
    () => emits("update-nodes", nodeArray.value)
)

async function enterNode(node) {
  fileNodes.value = await getFolderMetaList(node.id);
  nodeArray.value.push(node);
}

async function onGoBackOneLevel() {
  nodeArray.value.pop();
  if (nodeArray.value.length === 0) {
    fileNodes.value = await getFolderMetaList("");
  } else {
    fileNodes.value = await getFolderMetaList(nodeArray.value[nodeArray.value.length - 1].id);
  }
}

async function onGoRoot() {
  nodeArray.value = [];
  fileNodes.value = await getFolderMetaList("");
}
</script>

<style scoped>
.folder-tree-navigation-container {
  padding: 12px;
  margin-left: -24px;
  margin-right: -24px;
  background: var(--van-background);
}
</style>