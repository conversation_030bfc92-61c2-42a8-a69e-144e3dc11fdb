import { getJWTHeaders } from "./FetchUserAPI";

/**
 * 通用GET请求函数
 */
async function fetchGetRequest(url: string) {
  return fetch(
    `${url}${url.includes("?") ? "&" : "?"}t=${new Date().getTime()}`,
    {
      headers: {
        ...getJWTHeaders(),
        "Content-Type": "application/json",
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
      cache: "no-store",
    }
  ).then((res) => res.json());
}

export async function getVipLevels() {
  return fetchGetRequest("/api/vip/levels");
}

export async function getVipInfo() {
  return fetchGetRequest("/api/user/vip");
}

export async function createOrder(
  levelId: string,
  promotionId?: string,
  deviceType?: string
) {
  return fetch("/api/user/vip/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...getJWTHeaders(),
    },
    body: JSON.stringify({
      levelId,
      promotionId,
      deviceType: deviceType?.toUpperCase(),
    }),
  }).then((res) => res.json());
}

export async function confirmOrder(orderId: string) {
  return fetch("/api/user/vip/confirm", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...getJWTHeaders(),
    },
    body: JSON.stringify({
      orderId,
    }),
  }).then((res) => res.json());
}
