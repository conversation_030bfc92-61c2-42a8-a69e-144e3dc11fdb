<template>
  <div class="container">
    <el-image
      :src="src"
      fit="cover"
      :preview-src-list="previewSrcList"
      :initial-index="index"
      :preview-teleported="true"
      class="img-div"
    />
    <div class="delete-icon-div">
      <el-icon :size="22" @click="deleteImage">
        <CloseBold />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CloseBold } from "@element-plus/icons-vue";

const props = defineProps({
  index: {
    type: Number,
    default: 0,
  },
  src: {
    type: String,
    default: "",
  },
  previewSrcList: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(["delete"]);

function deleteImage() {
  emits("delete", props.index);
}
</script>

<style scoped>
.container {
  position: relative;
  height: calc(200px - 24px - 20px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-icon-div {
  position: absolute;
  top: 0;
  right: 0;
}

.img-div {
  max-height: calc(200px - 24px - 20px);
}
</style>
