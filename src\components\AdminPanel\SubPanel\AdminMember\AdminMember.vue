<template>
  <div v-loading="loading">
    <div style="margin-bottom: 15px; display: flex; justify-content: end">
      <el-button type="primary" @click="reload">刷新</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="50" />
      <el-table-column
        prop="name"
        label="用户昵称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="createdAt"
        label="注册时间"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="startDate" label="开始时间" />
      <el-table-column prop="endDate" label="结束时间" />
      <el-table-column prop="totalPrice" label="总付款金额" />
      <el-table-column fixed="right" label="操作" min-width="120">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="showDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 15px; display: flex; justify-content: center">
      <el-pagination
        v-model:current-page="currentPage"
        background
        layout="prev, pager, next"
        :page-count="totalPage"
      />
    </div>
    <el-dialog
      v-model="showDialog"
      title="详情"
      width="90%"
      top="50px"
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <MemberDetail :id="currentDetailMemberId" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { fetchMembers } from "@/components/AdminPanel/SubPanel/FetchAdminAPI";
import MemberDetail from "./MemberDetail.vue";

type MemberData = {
  id: string;
  name: string;
  vipStatus: {
    startDate: string;
    endDate: string;
  }[];
  createdAt: string;
};

const loading = ref(true);
const totalPage = ref(1);
const currentPage = ref(1);
const showDialog = ref(false);
const currentDetailMemberId = ref("");
const tableDataRaw = ref<MemberData[]>([]);
const tableData = computed(() => {
  return tableDataRaw.value.map((item) => {
    const startDate = item.vipStatus.reduce((prev, cur) => {
      return prev < cur.startDate ? prev : cur.startDate;
    }, item.vipStatus[0].startDate);
    const endDate = item.vipStatus.reduce((prev, cur) => {
      return prev > cur.endDate ? prev : cur.endDate;
    }, item.vipStatus[0].endDate);
    return {
      ...item,
      createdAt: new Date(item.createdAt).toLocaleString(),
      startDate: new Date(startDate).toLocaleString(),
      endDate: new Date(endDate).toLocaleString(),
    };
  });
});

onMounted(() => {
  fetchData(1);
});

watch(currentPage, (newPage) => {
  fetchData(newPage);
});

function reload() {
  fetchData(currentPage.value);
}

function fetchData(page: number) {
  loading.value = true;
  fetchMembers(page)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        tableDataRaw.value = data.data?.members ?? [];
        totalPage.value = data.pagination?.totalPages ?? 1;
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      loading.value = false;
    });
}

function showDetail(row: MemberData) {
  currentDetailMemberId.value = row.id;
  showDialog.value = true;
}
</script>
