<template>
  <div style="margin-left: 10px; margin-right: 10px">
    <div style="display: flex; align-items: center">
      <div
        v-if="!drawingEnabled"
        style="padding-left: 5px; padding-right: 15px"
      >
        <el-text type="primary" @click="addText">
          <i class="bi bi-keyboard-fill" style="font-size: 20px"></i>
        </el-text>
      </div>
      <div style="padding-right: 15px">
        <el-text v-if="!drawingEnabled" type="primary" @click="toggleDrawing">
          <i class="bi bi-pen" style="font-size: 15px"></i>
        </el-text>
        <el-button v-else type="success" size="small" @click="toggleDrawing">
          完成
        </el-button>
      </div>
      <!-- 暂时停用 -->
      <div v-if="false" style="padding-right: 15px">
        <el-upload
          :auto-upload="false"
          :on-change="addImage"
          :show-file-list="false"
          accept="image/*"
        >
          <el-text type="primary">
            <i class="bi bi-card-image" style="font-size: 20px"></i>
          </el-text>
        </el-upload>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  drawingEnabled: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["add-item", "toggle-drawing"]);

function addText() {
  emits("add-item", { type: "TEXT", value: "", isNew: true });
}

function toggleDrawing() {
  emits("toggle-drawing");
}

function addImage(uploadFile) {
  const fileUrl = URL.createObjectURL(uploadFile.raw);
  emits("add-item", { type: "IMAGE", value: fileUrl });
}
</script>
