<template>
  <el-card
    v-loading="loading"
    ref="noteCardRef"
    :body-style="{ padding: '0px' }"
    :style="{ borderColor: noteColor }"
    class="box-card"
    shadow="hover"
  >
    <div :class="{ 'note-card': cardEdit }">
      <StretchedLink v-if="!cardEdit" @click="clickCard" />
      <!-- <NoteCardHeader :index="rect.index || 1"
                      :style="{ backgroundColor: noteColor }"
                      :title="noteContent.noteTitle"
                      :card-edit="cardEdit"
                      @title-changed="onTitleChanged"
                      @toggle-edit="handleToggleEdit"
      /> -->
      <div v-if="!listMode || cardEdit" class="content-area">
        <div class="note-content">
          <div
            v-for="item in noteContent.noteContentArray"
            :key="item.uid"
            style="position: relative; min-height: 32px"
          >
            <div v-if="item.type === 'TEXT'">
              <NoteCardText
                :card-edit="cardEdit && !drawingEnabled"
                :item="item"
                @update-item="updateItem"
              />
            </div>
            <div v-if="item.type === 'IMAGE'">
              <NoteCardImage
                :card-edit="cardEdit && drawingEnabled"
                :item="item"
                @update-item="updateItem"
                @delete-item="handleDeleteItem"
              />
            </div>
            <div v-if="item.type === 'DRAWING'">
              <NoteFreeDrawing
                :item="item"
                :last-uid="lastItemUid"
                :edit-enabled="cardEdit && drawingEnabled"
                @update-item="updateItem"
                @delete-item="handleDeleteItem"
              />
            </div>
            <div
              v-if="cardEdit && !drawingEnabled"
              style="position: absolute; right: 8px; top: 2px"
            >
              <el-button text size="small" @click="handleDeleteItem(item)">
                <i class="bi bi-trash" />
              </el-button>
            </div>
          </div>
        </div>
        <div class="fixed-bottom">
          <NoteCardNewItem
            v-if="cardEdit"
            :fullscreen="fullscreen"
            :drawingEnabled="drawingEnabled"
            @add-item="addItem"
            @toggle-drawing="toggleDrawing"
            @toggle-fullscreen="toggleFullscreen"
            @delete-note="sendDeleteRectMessage"
          />
          <div>
            <el-divider border-style="dashed" style="margin: 5px" />
            <NoteCardTags
              :card-edit="cardEdit"
              :noteTags="noteContent.noteTags"
              @update-tags="updateTags"
            />
          </div>
        </div>
        <Teleport to="body">
          <NoteFreeDrawingPenToolbox v-if="drawingEnabled" />
        </Teleport>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import _ from "lodash";
import { computed, inject, nextTick, onMounted, ref } from "vue";
import { dialogInjectionKey } from "element-plus";
import { useObservable } from "@vueuse/rxjs";
import { liveQuery } from "dexie";
import {
  createNoteFromRect,
  deleteRectByUID,
  getNoteFromRect,
  saveNote,
} from "@/lib/RectDatabase.js";
import { LayerType, RECT_ACTION_TYPE } from "@/constant";
import { useOCRStore } from "@/stores/ocr.js";
import NoteCardHeader from "@/components/NotebookPanel/NoteCardCompt/NoteCardHeader.vue";
import NoteCardText from "@/components/NotebookPanel/NoteCardCompt/NoteCardText.vue";
import NoteCardImage from "@/components/NotebookPanel/NoteCardCompt/NoteCardImage.vue";
import NoteCardTags from "@/components/NotebookPanel/NoteCardCompt/NoteCardTags.vue";
import StretchedLink from "@/components/NotebookPanel/NoteCardCompt/StretchedLink.vue";
import NoteCardNewItem from "@/components/NotebookPanel/NoteCardCompt/NoteCardNewItem.vue";
import NoteFreeDrawing from "@/components/NotebookPanel/NoteCardCompt/NoteFreeDrawing.vue";
import NoteFreeDrawingPenToolbox from "@/components/NotebookPanel/NoteCardCompt/NoteFreeDrawingPenToolbox.vue";
import { useDraggable } from "@/components/NotebookPanel/util/use-draggable";

const props = defineProps({
  rect: {
    type: Object,
    required: true,
  },
  cardEdit: {
    type: Boolean,
    default: false,
  },
  listMode: {
    type: Boolean,
    default: false,
  },
  fullscreen: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["delete-item", "toggle-fullscreen", "handle-edit"]);

// dialog draggable hack
try {
  const dialogInjection = inject(dialogInjectionKey);
  if (dialogInjection) {
    const { dialogRef, headerRef } = dialogInjection;
    const draggable = computed(() => true);
    useDraggable(dialogRef, headerRef, draggable);
  }
} catch (e) {
  // ignored
}

const ocrStore = useOCRStore();
const loading = ref(true);
const noteCardRef = ref(null);
const drawingEnabled = ref(false);
const noteContent = useObservable(
  liveQuery(() => getNoteFromRect(props.rect.uid)),
  {
    initialValue: {
      noteTitle: "",
      noteContentArray: [],
      noteTags: [],
    },
  }
);

const noteColor = computed(() => {
  return props.rect.color ? props.rect.color : "rgb(254, 208, 48)";
});

const lastItemUid = computed(() => {
  if (noteContent.value.noteContentArray.length === 0) {
    return null;
  }

  if (props.cardEdit && drawingEnabled.value) {
    const lastItem = _.findLast(
      noteContent.value.noteContentArray,
      (item) => item.type === "DRAWING"
    );
    return lastItem?.uid ?? null;
  }

  return noteContent.value.noteContentArray[
    noteContent.value.noteContentArray.length - 1
  ].uid;
});

onMounted(() => {
  getNoteFromRect(props.rect.uid)
    .then((note) => {
      if (!note) {
        const newNote = createNoteFromRect(props.rect.fileId, props.rect.uid);
        saveNote(newNote);
        return newNote;
      }

      return note;
    })
    .then((note) => {
      if (
        !props.rect.dataUrl ||
        !ocrStore.ocrWorker ||
        note.ocrContent ||
        note.noteContentArray?.length > 0
      ) {
        loading.value = false;
        return;
      }

      // ocr识别
      wasmGetText(props.rect.dataUrl).then((resultText) => {
        note.ocrContent = resultText;
        // note.noteTitle = result.data.text;
        const ocrNoteContent = {
          uid: Date.now(),
          type: "TEXT",
          value: resultText,
          isContent: true,
        };
        note.noteContentArray = [ocrNoteContent, ...note.noteContentArray];
        saveNote(note);
        loading.value = false;

        if (!props.cardEdit) {
          nextTick(() => {
            scrollToComponent();
          });
        }
      });
    });
});

async function wasmGetText(imageBase64) {
  const imageResponse = await fetch(imageBase64);
  const imageBlob = await imageResponse.blob();
  const image = await createImageBitmap(imageBlob);

  await ocrStore.ocrWorker?.loadImage(image);
  return await ocrStore.ocrWorker?.getText();
}

function scrollToComponent() {
  if (noteCardRef.value) {
    noteCardRef.value?.$el.scrollIntoView({ behavior: "smooth", block: "end" });
  }
}

function onTitleChanged(newTitle) {
  noteContent.value.noteTitle = newTitle;
  saveNote(noteContent.value);
}

function handleToggleEdit() {
  emits("handle-edit", props.rect);
}

function addItem(item) {
  if (
    item.type === "DRAWING" &&
    noteContent.value.noteContentArray.some((item) => item.type === "DRAWING")
  ) {
    return;
  }

  // if last item is empty, replace it
  if (noteContent.value.noteContentArray.length > 0) {
    const lastItem =
      noteContent.value.noteContentArray[
        noteContent.value.noteContentArray.length - 1
      ];
    if (lastItem.value === "") {
      noteContent.value.noteContentArray[
        noteContent.value.noteContentArray.length - 1
      ] = item;
      return;
    }
  }
  noteContent.value.noteContentArray =
    noteContent.value.noteContentArray.concat({ uid: Date.now(), ...item });
  saveNote(noteContent.value);
}

function updateItem(item) {
  noteContent.value.noteContentArray = noteContent.value.noteContentArray.map(
    (i) => (i.uid === item.uid ? item : i)
  );
  saveNote(noteContent.value);
}

function handleDeleteItem(item) {
  noteContent.value.noteContentArray =
    noteContent.value.noteContentArray.filter((i) => i.uid !== item.uid);
  saveNote(noteContent.value);
}

function toggleDrawing() {
  if (props.cardEdit) {
    drawingEnabled.value = !drawingEnabled.value;

    if (!drawingEnabled.value) {
      return;
    }

    // noteContentArray最后一项不是"Drawing"，则添加一项
    if (
      noteContent.value.noteContentArray.length === 0 ||
      noteContent.value.noteContentArray[
        noteContent.value.noteContentArray.length - 1
      ].type !== "DRAWING"
    ) {
      addItem({ uid: Date.now(), type: "DRAWING", value: "" });
    }
  }
}

function updateTags(tags) {
  noteContent.value.noteTags = tags;
  saveNote(noteContent.value);
}

function toggleFullscreen() {
  emits("toggle-fullscreen");
}

function clickCard() {
  jumpToPosition();
}

function jumpToPosition() {
  // todo: post message by store action
  const iframe = document.getElementById("mobsf4");
  if (iframe) {
    const data = {
      layerType: LayerType.RECT,
      type: RECT_ACTION_TYPE.JUMP,
      rect: props.rect.uid,
    };
    iframe.contentWindow.postMessage(data, "*");
  }
}

function sendDeleteRectMessage() {
  emits("delete-item");
  // deleteRectByUID(props.rect.uid);
}
</script>

<style lang="scss" scoped>
.box-card {
  position: relative;
  padding: 0;
  border-radius: 8px;
  width: 100%;
}

.note-card {
  height: 80vh;
  display: flex;
  flex-flow: column;

  .content-area {
    flex: 1;
    display: flex;
    flex-flow: column;
    overflow: hidden;

    .note-content {
      flex: 1;
      padding: 10px;
      overflow-y: scroll;
    }
  }
}

.fixed-bottom {
  background-color: white;
  z-index: 1;
}
</style>
