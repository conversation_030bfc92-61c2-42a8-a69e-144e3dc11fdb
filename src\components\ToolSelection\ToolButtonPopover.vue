<template>
  <div v-if="aliveToolIndex === tool.toolIndex" class="tools-button-outer">
    <el-drawer
      v-model="showDrawer"
      title="工具自定义"
      :append-to-body="true"
      :size="isVertical ? '70%' : drawerLandscapeWidth"
      :direction="isVertical ? 'btt' : 'rtl'"
    >
      <!-- 选择颜色 -->
      <ColorSelect
        :aliveColor="currentTool.aliveColor"
        :currentTool="currentTool"
        @color-changed="changeAliveColor"
      />
      <!-- 选择线宽 -->
      <WidthSlider
        :currentTool="currentTool"
        @change-line-width="changeLineWidth"
      />
      <!-- 选择线型 -->
      <LineTypeSwitch
        :currentTool="currentTool"
        @change-line-type="changeLineType"
      />
    </el-drawer>
    <div
      v-if="
        [
          'PEN',
          'HIGHLIGHT_PEN',
          'HIGHLIGHT_PEN_STRAIGHT',
          'ERASER',
          'LINE',
          'DOTTED_LINE',
        ].includes(tool.type)
      "
      class="tools-button-inner-alive"
      :style="{ borderColor: currentTool.aliveColor }"
      @click="showDrawer = true"
    >
      <img
        alt=""
        class="btn-tools-alive"
        :src="getActiveToolImage"
        :style="{ backgroundColor: currentTool.aliveColor }"
      />
    </div>
    <div
      v-else
      class="tools-button-inner-alive"
      :style="{ borderColor: currentTool.aliveColor }"
    >
      <img
        alt=""
        class="btn-tools-alive"
        :src="getActiveToolImage"
        :style="{ backgroundColor: currentTool.aliveColor }"
      />
    </div>
  </div>
  <div v-else @click="changeTool" class="tools-button-outer">
    <div class="tools-button-inner">
      <img
        alt=""
        class="btn-tools"
        :src="getToolImage"
        :style="{ backgroundColor: currentTool.aliveColor }"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, computed, ref } from "vue";
import { useWindowSize } from "@vueuse/core";
import ColorSelect from "@/components/ToolSelection/ColorSelect.vue";
import WidthSlider from "@/components/ToolSelection/WidthSlider.vue";
import LineTypeSwitch from "@/components/ToolSelection/LineTypeSwitch.vue";

import pen_ap from "@/assets/image/pen_ap_s.png";
import lightpen from "@/assets/image/lightpen_ap.png";
import strline_ap from "@/assets/image/strline_ap.png";
import copyword_full from "@/assets/image/a_line_yellow_2.png";
import copyword_red_full from "@/assets/image/a_line_yellow_2.png";
import eraser_ap from "@/assets/image/move_ap.png";
import eraser from "@/assets/image/move_blue_white.png";
import dotted_line_ap from "@/assets/image/pen_dotted_line_ap.png";

const props = defineProps({
  tool: {
    type: Object,
    required: true,
  },
  aliveToolIndex: {
    type: Number,
    required: true,
  },
});
const emit = defineEmits(["tool-changed"]);
const changeTool = () => {
  emit("tool-changed", currentTool);
};

const showDrawer = ref(false);
const { width, height } = useWindowSize();
const isVertical = computed(() => width.value > height.value);
const drawerLandscapeWidth = computed(() =>
  width.value < 900 ? "300px" : "30%"
);

// '当前工具'
const currentTool = reactive({
  toolIndex: props.tool.toolIndex || 0,
  type: props.tool.type || "",
  aliveColor: props.tool.aliveColor || "",
  lineWidth: props.tool.lineWidth || 0,
});

const changeAliveColor = (color) => {
  currentTool.aliveColor = color;
  emit("tool-changed", currentTool);
};

const changeLineWidth = (lineWidth) => {
  currentTool.lineWidth = lineWidth;
  emit("tool-changed", currentTool);
};

const changeLineType = (lineType) => {
  currentTool.type = lineType;
  emit("tool-changed", currentTool);
};

const toolsImage = ref([
  {
    type: "BOX_SELECT",
    image: copyword_full,
    activeImage: copyword_red_full,
  },
  {
    type: "PEN",
    image: pen_ap,
    activeImage: pen_ap,
  },
  {
    type: "HIGHLIGHT_PEN",
    image: lightpen,
    activeImage: lightpen,
  },
  {
    type: "HIGHLIGHT_PEN_STRAIGHT",
    image: lightpen,
    activeImage: lightpen,
  },
  {
    type: "LINE",
    image: strline_ap,
    activeImage: strline_ap,
  },
  {
    type: "DOTTED_LINE",
    image: dotted_line_ap,
    activeImage: dotted_line_ap,
  },
  {
    type: "ERASER",
    image: eraser,
    activeImage: eraser_ap,
  },
]);

const getToolImage = computed(() => {
  const tool = toolsImage.value.find((t) => t.type === props.tool.type);
  return tool ? tool.image : "";
});

const getActiveToolImage = computed(() => {
  const tool = toolsImage.value.find((t) => t.type === props.tool.type);
  return tool ? tool.activeImage : "";
});
</script>
<style scoped>
.btn-tools {
  width: 25px;
  height: 25px;
}

.btn-tools-alive {
  width: 20px;
  height: 20px;
}

.tools-button-outer {
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.tools-button-inner-alive {
  border: 2px solid;
  border-radius: 5px;
  width: 32px;
  height: 32px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.tools-button-inner {
  width: 32px;
  height: 32px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.color-button-circle {
  padding: 10px;
  font-size: 1.5rem;
}

.btn-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
</style>
