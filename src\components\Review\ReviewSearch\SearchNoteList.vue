<template>
    <div style="margin-left: 10px;margin-right: 10px;margin-top: 0px;">
        <el-scrollbar :style="{ height: scrollbarHeight - 100 + 'px' }" ref="scrollbar">
            <div style="background-color: #f4f4f5;padding-bottom: 20px;">
                <van-cell-group inset v-for="item in filelist" :key="item.id">
                    <template #title>
                        <div class="custom-title">{{ item.name }}</div>
                    </template>
                    <van-cell v-for="note in item.notelist" :key="note.title" :title="note.title" :label="note.info"
                        @click="openFile(item,note)" />
                </van-cell-group>
            </div>
            <div style="margin-top: 10px;margin-bottom: 10px;">
                <h4 style="text-align: center;color: #409EFF;">加载更多</h4>
            </div>
        </el-scrollbar>
    </div>
    <PreviewNoteFile ref="previewNoteFileRef" />
</template>

<script setup>
import { ref, onMounted } from 'vue';
import PreviewNoteFile from "@/components/Research/PreviewNoteFile.vue";

const props = defineProps({
  keyWord: {
    type: String,
    default: ''
  }
})
const previewNoteFileRef = ref(null);

const scrollbar = ref(null);
const scrollbarHeight = ref(0);


onMounted(() => {
    console.log('mounted 根据keyword 获取filelist',props.keyWord);
    const scrollbarY = scrollbar.value.$el.getBoundingClientRect().top;
    const viewportHeight = window.innerHeight;
    scrollbarHeight.value = viewportHeight - scrollbarY;
});

const openFile = (file,note) => {
    previewNoteFileRef.value.showPreview(file,note);

}

const filelist = [
    { "id": "1", "name": "2014年河北省廊坊市广阳区初中英语中考试卷（带解析）", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "5", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "red" }, { "title": "Note 2", "info": "笔记card文字", "color": "blue" }] },
    { "id": "2", "name": "2014年河北省廊坊市广阳区初中英语中考试卷（带解析）", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "9", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "green" }, { "title": "Note 2", "info": "笔记card文字", "color": "yellow" }] },
    { "id": "3", "name": "文件3", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "10", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "purple" }, { "title": "Note 2", "info": "笔记card文字", "color": "orange" }] },
    { "id": "4", "name": "2014年河北省廊坊市广阳区初中英语中考试卷（带解析）", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "9", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "green" }, { "title": "Note 2", "info": "笔记card文字", "color": "yellow" }] },
    { "id": "5", "name": "File 1", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "10", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "black" }, { "title": "Note 2", "info": "笔记card文字", "color": "white" }] },
    { "id": "6", "name": "File 1", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "10", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "black" }, { "title": "Note 2", "info": "笔记card文字", "color": "white" }] },
    { "id": "7", "name": "File 1", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "10", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "black" }, { "title": "Note 2", "info": "笔记card文字", "color": "white" }] },
    { "id": "8", "name": "File 2", "image": "https://www.51bxbj.com/newweb/upload/2024/02/05/65c06366db03438712f8", "lastModified": "2022/01/12 20:40", "notesCount": "10", "notelist": [{ "title": "Note 1", "info": "笔记card文字", "color": "black" }, { "title": "Note 2", "info": "笔记card文字", "color": "white" }] }
];

</script>

<style scoped>
.custom-title {
    color: #000000;
    font-size: 15px;
}

</style>