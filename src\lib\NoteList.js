import <PERSON>ie from 'dexie';

const db = new <PERSON><PERSON>('noteList');
db.version(1).stores({
  notes: 'uid, name',
});

export async function saveRectsToNotes(filename, fileId, rects){
  const list = rects.map(r => {
    return {
      uid: r.uid,
      fileId,
      name: filename,
      title: r.note.noteTitle,
      notes: [
        ...JSON.parse(JSON.stringify(r.note.noteContentArray))
      ]
    }
  })

  return db.notes.bulkPut(list);
}

export async function searchNotesByKeyword(keyword) {
  const reg = new RegExp(keyword.trim(), 'i');
  return db.notes.filter(note =>  note?.notes?.some(n => n.type === "TEXT" && reg.test(n.value))).toArray();
}