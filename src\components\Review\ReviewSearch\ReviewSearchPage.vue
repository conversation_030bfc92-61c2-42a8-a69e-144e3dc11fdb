<template>
  <van-nav-bar title="复习" left-text="返回" left-arrow @click-left="goBack" />
  <SearchComponent @on-search="onSearch" />
  <div>
    <SearchColorComponent @on-search="onTagSearch" />
    <van-row>
      <van-col v-if="searchKeyWord || searchColorTag" span="24">
        <ReviewNotebookPanel
          :search-keyword="searchKeyWord"
          :search-color-tag-id="searchColorTag?.id"
        />
      </van-col>
      <van-col v-else span="24">
        <div style="text-align: center">
          <img src="@/assets/image/cactus.png" />
          <h3>选择分类 显示笔记</h3>
        </div>
      </van-col>
    </van-row>
  </div>
  <BottomTabBar active="note" />
</template>

<script setup>
import { useRouter } from "vue-router";
import { ref } from "vue";
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";
import SearchComponent from "@/components/Review/ReviewSearch/ReviewSearchComponent.vue";
import SearchColorComponent from "@/components/Review/ReviewSearch/ReviewSearchColorComponent.vue";
import ReviewNotebookPanel from "@/components/Review/ReviewSearch/ReviewNotebookPanel.vue";

const router = useRouter();
const searchKeyWord = ref("");
const searchColorTag = ref(null);

const goBack = () => {
  router.push("/");
};

function onSearch(keyWord) {
  searchKeyWord.value = keyWord;
}

function onTagSearch(tag) {
  searchColorTag.value = tag;
}
</script>

<style scoped></style>
