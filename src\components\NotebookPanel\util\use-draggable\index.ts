import { onBeforeUnmount, onMounted, watchEffect } from 'vue';
import { addUnit } from './add-unit';
import type { ComputedRef, Ref } from 'vue';

export const useDraggable = (
    targetRef: Ref<HTMLElement | undefined>,
    dragRef: Ref<HTMLElement | undefined>,
    draggable: ComputedRef<boolean>
) => {
    let transform = {
        offsetX: 0,
        offsetY: 0,
    }

    const onMousedown = (e: MouseEvent | TouchEvent) => {
        const downX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
        const downY = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY
        const { offsetX, offsetY } = transform

        const targetRect = targetRef.value!.getBoundingClientRect()
        const targetLeft = targetRect.left
        const targetTop = targetRect.top
        const targetWidth = targetRect.width
        const targetHeight = targetRect.height

        const clientWidth = document.documentElement.clientWidth
        const clientHeight = document.documentElement.clientHeight

        const minLeft = -targetLeft + offsetX
        const minTop = -targetTop + offsetY
        const maxLeft = clientWidth - targetLeft - targetWidth + offsetX
        const maxTop = clientHeight - targetTop - targetHeight + offsetY

        const onMousemove = (e: MouseEvent | TouchEvent) => {
            const clientX = e instanceof MouseEvent ? e.clientX : e.touches[0].clientX
            const clientY = e instanceof MouseEvent ? e.clientY : e.touches[0].clientY
            const moveX = Math.min(
                Math.max(offsetX + clientX - downX, minLeft),
                maxLeft
            )
            const moveY = Math.min(
                Math.max(offsetY + clientY - downY, minTop),
                maxTop
            )

            transform = {
                offsetX: moveX,
                offsetY: moveY,
            }

            if (targetRef.value) {
                targetRef.value.style.transform = `translate(${addUnit(
                    moveX
                )}, ${addUnit(moveY)})`
            }
        }

        const onMouseup = () => {
            document.removeEventListener('mousemove', onMousemove)
            document.removeEventListener('mouseup', onMouseup)
            document.removeEventListener('touchmove', onMousemove)
            document.removeEventListener('touchend', onMouseup)
        }

        document.addEventListener("touchmove", onMousemove);
        document.addEventListener("touchend", onMouseup);
        document.addEventListener('mousemove', onMousemove)
        document.addEventListener('mouseup', onMouseup)
    }

    const onDraggable = () => {
        if (dragRef.value && targetRef.value) {
            dragRef.value.addEventListener('mousedown', onMousedown)
            dragRef.value.addEventListener('touchstart', onMousedown)
        }
    }

    const offDraggable = () => {
        if (dragRef.value && targetRef.value) {
            dragRef.value.removeEventListener('mousedown', onMousedown)
            dragRef.value.removeEventListener('touchstart', onMousedown)
        }
    }

    onMounted(() => {
        watchEffect(() => {
            if (draggable.value) {
                onDraggable()
            } else {
                offDraggable()
            }
        })
    })

    onBeforeUnmount(() => {
        offDraggable()
    })
}
