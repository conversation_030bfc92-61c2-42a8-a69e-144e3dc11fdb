import { defineStore } from "pinia";
import { computed, ref } from "vue";

type UserProfileData = {
  user: {
    name: string;
  };
  vip: {
    isVIP: boolean;
    endDate: string;
  };
  isAdmin: boolean;
};

export const useProfileStore = defineStore("profile", () => {
  const userProfile = ref<UserProfileData | null>(null);
  const hideVIPFeature = ref<boolean>(false);

  // 当freeTrialCount < 0时，表示会员，不检查是否还有剩余试用次数
  const freeTrialCount = ref(0);

  // 当freeTrialCount < -1时为会员，不检查是否还有剩余试用次数
  const haveTrialPermission = computed(
    () =>
      freeTrialCount.value !== 0 ||
      userProfile.value?.vip?.isVIP ||
      hideVIPFeature.value
  );
  const isVIP = computed(
    () => userProfile.value?.vip?.isVIP || hideVIPFeature.value
  );
  const isAdmin = computed(() => userProfile.value?.isAdmin);

  // read from localStorage
  const syncFreeTrialCount = () => {
    const count = localStorage.getItem("free_trial_count");
    if (count) {
      freeTrialCount.value = parseInt(count);
    } else {
      freeTrialCount.value = 10;
    }
  };

  // write to localStorage
  const reduceFreeTrialCount = () => {
    if (freeTrialCount.value > 0 && !isVIP.value && !hideVIPFeature.value) {
      freeTrialCount.value -= 1;
      localStorage.setItem("free_trial_count", freeTrialCount.value.toString());
    }
  };

  return {
    userProfile,
    hideVIPFeature,
    freeTrialCount,
    syncFreeTrialCount,
    reduceFreeTrialCount,
    haveTrialPermission,
    isVIP,
    isAdmin,
  };
});
