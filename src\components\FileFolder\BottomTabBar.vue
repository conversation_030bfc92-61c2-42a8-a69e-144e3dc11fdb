<template>
  <van-tabbar v-model="activeTab" active-color="#ee0a24" @change="onChange">
    <van-tabbar-item icon="column" name="file">阅读 笔记</van-tabbar-item>
    <van-tabbar-item icon="invitation" name="note">复习</van-tabbar-item>
    <van-tabbar-item icon="user" name="profile">我的</van-tabbar-item>
  </van-tabbar>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

const props = defineProps({
  active: {
    type: String,
    default: "file",
  },
});

const router = useRouter();
const activeTab = ref(props.active);
function onChange(name) {
  if (name === "file") {
    router.replace({ path: "/" });
  } else if (name === "note") {
    router.replace({ path: "/review-search" });
  } else if (name === "profile") {
    router.replace({ path: "/profile" });
  }
}
</script>
