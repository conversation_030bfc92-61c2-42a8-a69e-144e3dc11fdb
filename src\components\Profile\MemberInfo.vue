<template>
  <el-main v-loading="loading">
    <VersionInfo :show-back="true" />
    <div v-if="showMemberInfo">
      <el-row justify="center" style="margin: 10px 0 20px 0">
        <el-col :sm="16" :lg="12">
          <AvatarInfo :emit-login="true" @login="showLoginDialog = true" />
        </el-col>
      </el-row>
      <el-row justify="center">
        <el-col :sm="16" :lg="12">
          <VIPPayLevel
            @refresh-pay="refreshPay"
            @show-login="showLoginDialog = true"
          />
        </el-col>
      </el-row>
      <el-row justify="center" style="margin-top: 2rem">
        <el-col :sm="16" :lg="12">
          <h3 style="margin-bottom: 1rem; font-weight: bold">会员权益</h3>
          <p class="member-des-item">
            <el-icon :size="24" class="icon"><CameraFilled /></el-icon>
            不限次、不限篇幅拍照识别
          </p>
          <p class="member-des-item">
            <el-icon :size="24" class="icon"><DocumentAdd /></el-icon>
            安卓、iOS多终端均可使用
          </p>
          <p class="member-des-item">
            <el-icon :size="24" class="icon"><CircleCloseFilled /></el-icon>
            去广告
          </p>
        </el-col>
      </el-row>
    </div>
    <el-drawer
      v-model="showLoginDialog"
      title="登录"
      direction="btt"
      size="100%"
      :append-to-body="true"
    >
      <LoginDialog @login-success="showLoginDialog = false" />
    </el-drawer>
    <div style="margin-bottom: 50px"></div>
    <BottomTabBar active="profile" />
  </el-main>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
// @ts-expect-error 导入js组件
import { useAppStore } from "@/stores/app";
import { useProfileStore } from "@/stores/profile";
import VersionInfo from "@/components/Profile/VersionInfo.vue";
import AvatarInfo from "@/components/Profile/AvatarInfo.vue";
import VIPPayLevel from "@/components/Profile/VIPCompt/VIPPayLevel.vue";
import LoginDialog from "@/components/Profile/LoginPage/LoginDialog.vue";
// @ts-expect-error 导入js组件
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";
import { confirmOrder } from "@/components/Profile/Requests/FetchVipApi";
import { fetchUserProfile } from "@/components/Profile/Requests/FetchUserAPI";

const appStore = useAppStore();
const profileStore = useProfileStore();
const loading = ref(true);
const showMemberInfo = ref(false);
const showLoginDialog = ref(false);

onMounted(() => {
  loading.value = true;
  fetchUserProfile()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        profileStore.userProfile = data.data;
      } else {
        profileStore.userProfile = null;
      }
    })
    .catch(() => {})
    .finally(() => {
      showMemberInfo.value = true;
      loading.value = false;
    });
});

function refreshPay(orderId: string) {
  if (!orderId) {
    return;
  }

  loading.value = true;
  confirmOrder(orderId)
    .then((res) => {
      if (res.resCode && res.resCode === "SUCCESS") {
        appStore.globalRouterRefreshKey++;
        ElMessage.success(res.message ?? "开通成功");
      } else if (res.message) {
        ElMessage.error(res.message);
      } else {
        ElMessage.error("获取用户账户信息失败");
      }
    })
    .catch((err) => {
      ElMessage.error(err.message ?? "获取用户账户信息失败");
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style lang="scss" scoped>
.member-des-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  .icon {
    color: #b0926eff;
    margin-right: 0.5rem;
  }
}
</style>
