export const PresetColors = [
    "rgb(254, 208, 48)",
    "rgb(21, 126, 251)",
    "rgb(252, 49, 66)",
    "rgb(83, 215, 105)",
    "rgb(155, 155, 155)",
]
export const ColorTags = [
    { id: 1, color: 'rgb(255, 225, 0)', name: '生词' },
    { id: 2, color: 'rgb(168, 219, 0)', name: '高频词' },
    { id: 3, color: 'rgb(0, 203, 255)', name: '固定搭配' },
    { id: 4, color: 'rgb(143, 164, 209)', name: '长难句' },
    { id: 5, color: 'rgb(205, 150, 255)', name: '非谓语动词' },
    { id: 6, color: 'rgb(255, 160, 50)', name: '语法' },
];
export const DefaultColor = "rgb(21, 126, 251)";

export const colorTagsSelect = [
    "rgb(0, 0, 0)",
    "rgb(99, 99, 99)",
    "rgb(155, 155, 155)",
    "rgb(210, 210, 210)",
    "rgb(6, 122, 254)",
    "rgb(129, 34, 139)",
    "rgb(210, 19, 19)",
    "rgb(247, 83, 82)",
    "rgb(255, 151, 150)",
    "rgb(245, 154, 37)",
    "rgb(21, 126, 251)",
    "rgb(23, 69, 143)",
    "rgb(254, 208, 48)",
    "rgb(83, 215, 105)",
    "rgb(126, 198, 54)",
];
