<template>
    <div style="margin-left: 10px;margin-right: 10px;margin-top: 5px;">
        <el-text>标签</el-text>
        <van-checkbox-group v-model="checked" shape="square" @change="handleChange" style="display: flex; flex-wrap: wrap;">
            <van-checkbox v-for="(tag, index) in tagList" :key="index" :name="tag.name" style="margin: 8px;">
                {{ tag.name }}
            </van-checkbox>
        </van-checkbox-group>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import tagList from '@/components/FileFolder/tagList.json';

const checked = ref([]);
const handleChange = (value) => {
    console.log(value);
    console.log(checked.value);
}

</script>
<style scoped></style>
 