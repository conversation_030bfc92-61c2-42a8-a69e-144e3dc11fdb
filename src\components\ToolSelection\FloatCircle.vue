<!--
  可拖动位置工具栏，内容为三个固定的笔、手、旋转工具栏方向。
-->
<template>
  <div>
    <div
      class="toolslist-fixed"
      ref="toolsListFixed"
      @mousedown="dragStart"
      @mousemove="drag"
      @mouseup="dragEnd"
      @touchstart="dragStart"
      @touchmove="drag"
      @touchend="dragEnd"
      :style="toolsListFixedStyle"
    >
      <ToolButtonPopover
        v-for="tool in tools"
        :key="tool.type"
        :tool="tool"
        :aliveToolIndex="aliveToolIndex"
        :isVertical="isVertical"
        @tool-changed="toolChanged"
      />
      <div
        v-if="showFileNoteBtn"
        class="tools-button-outer"
        @click="showFileNotes"
      >
        <div alt="" class="btn-tools" style="pointer-events: none">
          <el-icon :size="25" style="color: #9fafc2ff"><Notebook /></el-icon>
        </div>
      </div>
      <div class="tools-button-outer" @click="toolbarFlip">
        <img
          alt=""
          class="btn-tools"
          src="@/assets/image/fanzhuan_ap_white.png"
          style="pointer-events: none"
        />
      </div>
      <slot name="extra-btn"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { useResizeObserver } from "@vueuse/core";
import ToolButtonPopover from "@/components/ToolSelection/ToolButtonPopover.vue";

const props = defineProps({
  customTools: {
    type: Array,
    required: false,
    default: () => [],
  },
  defaultDirection: {
    type: String,
    required: false,
    default: "column",
  },
  defaultPosition: {
    type: Object,
    required: false,
    default: () => ({
      top: "200px",
      left: "40px",
    }),
  },
  resetPositionKey: {
    type: Number,
    required: false,
    default: 0,
  },
  showFileNoteBtn: {
    type: Boolean,
    default: false,
  },
});

const aliveToolIndex = ref(1);
const tools = ref([]);
// 默认的工具列表
const defaultTools = ref([
  {
    toolIndex: 1,
    type: "BOX_SELECT",
    aliveColor: "rgb(203, 83, 86)", //用于图标背景和边框 不参与实际书写区域的功能
    lineWidth: 0,
  },
  {
    toolIndex: 2,
    type: "PEN",
    aliveColor: "rgb(210, 19, 19)",
    lineWidth: 5,
  },
  {
    toolIndex: 3,
    type: "HIGHLIGHT_PEN_STRAIGHT",
    aliveColor: "rgb(245, 154, 37)",
    lineWidth: 14,
  },
  {
    toolIndex: 4,
    type: "HIGHLIGHT_PEN_STRAIGHT",
    aliveColor: "rgb(83, 215, 105)",
    lineWidth: 14,
  },
  {
    toolIndex: 5,
    type: "ERASER",
    aliveColor: "rgb(213, 114, 114)", //用于图标背景和边框 不参与实际书写区域的功能
    lineWidth: 14,
  },
]);

//拖动工具栏移动
let dragging = ref(false);
let dragStartX = ref(0);
let dragStartY = ref(0);

const dragStart = (event) => {
  dragging.value = true;
  if (event.type === "touchstart") {
    dragStartX.value =
      event.touches[0].clientX - toolsListFixed.value.offsetLeft;
    dragStartY.value =
      event.touches[0].clientY - toolsListFixed.value.offsetTop;
  } else {
    dragStartX.value = event.clientX - toolsListFixed.value.offsetLeft;
    dragStartY.value = event.clientY - toolsListFixed.value.offsetTop;
  }
};

const drag = (event) => {
  if (dragging.value) {
    let clientX, clientY;
    if (event.type === "touchmove") {
      clientX = event.touches[0].clientX;
      clientY = event.touches[0].clientY;
    } else {
      clientX = event.clientX;
      clientY = event.clientY;
    }
    let newLeft = clientX - dragStartX.value;
    let newTop = clientY - dragStartY.value;
    // 限制工具栏 拖动范围左边和顶部不能超出屏幕，右边和底部可超出屏幕
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const edgeDistance = 25; // 设置边缘距离
    if (newLeft < 0) {
      newLeft = 25;
    } else if (newLeft > screenWidth - edgeDistance) {
      newLeft = screenWidth - edgeDistance;
    }
    if (newTop < 0) {
      newTop = 25;
    } else if (newTop > screenHeight - edgeDistance) {
      newTop = screenHeight - edgeDistance;
    }
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      left: `${newLeft}px`,
      top: `${newTop}px`,
    };
  }
};

const dragEnd = () => {
  dragging.value = false;
};

watch(
  () => props.resetPositionKey,
  () => {
    toolsListFixedStyle.value = {
      top: props.defaultPosition.top,
      left: props.defaultPosition.left,
      flexDirection: props.defaultDirection,
    };
  }
);

// 横竖屏切换重置位置
useResizeObserver(document.body, () => {
  if (props.resetPositionKey > 0) {
    return;
  }
  // 检查toolsListFixed的位置是否超出屏幕
  const rect = toolsListFixed.value.getBoundingClientRect();
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;
  const edgeDistance = 25; // 设置边缘距离
  if (rect.left < 0) {
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      left: `${edgeDistance}px`,
    };
  } else if (rect.right > screenWidth) {
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      left: `${screenWidth - rect.width - edgeDistance}px`,
    };
  }
  if (rect.top < 0) {
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      top: `${edgeDistance}px`,
    };
  } else if (rect.bottom > screenHeight) {
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      top: `${screenHeight - rect.height - edgeDistance}px`,
    };
  }
});

// 工具栏方向切换 横向和竖向
const isVertical = ref(true); // 默认是竖向排列
const toolsListFixed = ref(null); // 新增一个 ref 来获取 toolsListFixed 的引用
const toolsListFixedStyle = ref({
  top: props.defaultPosition.top,
  left: props.defaultPosition.left,
  flexDirection: props.defaultDirection,
});
const toolbarFlip = () => {
  isVertical.value = !isVertical.value;
  if (isVertical.value) {
    // 竖向排列
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      flexDirection: "column",
    };
  } else {
    // 横向排列
    toolsListFixedStyle.value = {
      ...toolsListFixedStyle.value,
      flexDirection: "row",
    };
  }
};

const emit = defineEmits(["change-tool-current", "show-file-notes"]);

function toolChanged(tool) {
  emit("change-tool-current", tool);
  aliveToolIndex.value = tool.toolIndex;
  if (tool.toolIndex) {
    tools.value.forEach((item) => {
      if (item.toolIndex === tool.toolIndex) {
        item.type = tool.type;
        item.aliveColor = tool.aliveColor;
        item.lineWidth = tool.lineWidth;
      }
    });
    // todo:需要调用APP通信工具类，将用户自定义的工具配置存储到APP沙盒中
  }
}

function showFileNotes() {
  emit("show-file-notes");
}

const getToolConfig = () => {
  // todo:需要一个APP通信的工具类，调取APP沙盒中存储的用户自定义APP配置 获取tools数组 如果没有返回任何内容，使用默认的工具列表
  return props.customTools && props.customTools.length > 0
    ? props.customTools
    : defaultTools.value; //临时使用默认工具列表
};

onMounted(() => {
  tools.value = getToolConfig();
});
</script>

<style scoped>
.btn-tools {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tools-start {
  width: 50px;
  height: 50px;
}

.tools-button-outer {
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  display: flex;
}

.toolslist-fixed {
  position: fixed;
  top: 200px;
  left: 40px;
  transform: translate(-25px, -25px);
  /* background-color: #f2f2f2; */
  background-color: #ffffff;
  border-radius: 10px;
  border: 1px solid #ccc;
  display: flex;
  flex-direction: column;
}
</style>
