<template>
  <div class="textarea-container">
    <div class="mirror-div">{{ text || "请输入内容" }}</div>
    <textarea
      ref="inputRef"
      v-model="text"
      :readonly="readonly"
      :rows="rows"
      @focus="onInputFocus"
      @blur="onTextChanged"
      class="auto-height-textarea"
      :style="inputStyle"
      placeholder="请输入内容"
    ></textarea>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, nextTick } from "vue";

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  rows: {
    type: Number,
    default: 2,
  },
  transparent: {
    type: Boolean,
    default: false,
  },
  autoFocus: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["text-changed", "on-focus"]);
const text = ref(props.text);
const isEditing = ref(false);
const inputRef = ref(null);

watch(
  () => props.text,
  () => (text.value = props.text)
);

const inputStyle = computed(() => {
  if (!props.transparent) {
    return {};
  }

  return {
    backgroundColor: isEditing.value ? "white" : "transparent",
  };
});

const onInputFocus = () => {
  isEditing.value = true;
  emits("on-focus");
};

const onTextChanged = () => {
  isEditing.value = false;
  emits("text-changed", text.value);
};

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      if (props.autoFocus) {
        inputRef.value?.focus();
        emits("on-focus");
      }
    }, 500);
  });
});
</script>

<style scoped>
.textarea-container {
  position: relative;
  width: 100%;
}

.auto-height-textarea {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  resize: none;
  overflow: hidden;
  border: none;
  transition: height 0.2s;
  /* background-color: #F0F2F5; */
  background-color: white;
  border-radius: 5px;
  padding: 8px;
  word-break: break-word;
  white-space: pre-wrap;
  box-sizing: border-box;
}

.mirror-div {
  visibility: hidden;
  pointer-events: none;
  word-break: break-word;
  white-space: pre-wrap;
  width: 100%;
  min-height: 2em;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  box-sizing: border-box;
  padding: 8px;
  color: transparent;
  background-color: white;
  border: none;
}
</style>
