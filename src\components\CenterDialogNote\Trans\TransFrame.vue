<template>
  <div
    class="trans-outer-div"
    :class="outerDivClasses"
    :style="outDivFullscreenStyle"
  >
    <!-- 用户能设置不同的翻译引擎，比如搜狗、有道、金山等 从原生APP中获取用户的配置显示对应的翻译 -->
    <!-- 网易有道翻译 -->
    <iframe
      class="iframe-WY"
      :src="srcWY"
      frameborder="0"
      :style="iframeFullscreenStyle"
    ></iframe>
    <!-- 金山词霸翻译 -->
    <!-- <iframe class="iframe-JS" :src="srcJS" frameborder="0"></iframe> -->
  </div>
</template>

<script setup>
import { computed } from "vue";
import { ref, watch } from "vue";
const props = defineProps({
  keyWord: {
    type: String,
    required: true,
  },
  fullscreen: {
    type: Boolean,
    default: false,
  },
  landscape: {
    type: Boolean,
    default: false,
  },
});

const srcWY = ref("");
// const srcSG = "https://fanyi.sogou.com/text?fr=default&keyword=apple&transfrom=auto&transto=zh-CHS&model=general"
// const srcJS = ref(`https://m.iciba.com/word?w=${props.keyWord}`)

const outerDivClasses = computed(() => {
  return props.landscape ? "landscape" : "";
});

const outDivFullscreenStyle = computed(() => {
  return props.fullscreen
    ? {
        width: "100%",
        height: "calc(100vh - 101px)",
        overflow: "hidden",
      }
    : {};
});

const iframeFullscreenStyle = computed(() => {
  return props.fullscreen
    ? {
        width: "100%",
        height: "calc(100vh - 101px)",
        overflow: "hidden",
      }
    : {};
});

watch(
  () => props.keyWord,
  () => {
    srcWY.value = `https://m.youdao.com/m/result?word=${props.keyWord}&lang=en`;
  },
  { immediate: true }
);
</script>

<style scoped>
.iframe-WY {
  width: 100%;
  /* height: 580px; */
  height: calc(75vh - 101px + 30%);
  box-sizing: border-box;
  /* transform: translateY(-80px); */
  margin-top: -30%;
}

.iframe-JS {
  width: 100%;
  /* height: 583px; */
  height: calc(75vh - 101px);
  box-sizing: border-box;
  /* transform: translateY(-83px); */
}
</style>

<style lang="scss" scoped>
.trans-outer-div {
  height: calc(75vh - 101px);
  overflow: hidden;

  @media screen and (max-width: 576px) {
    width: 270px;
  }
  @media screen and (min-width: 576px) and (max-width: 768px) {
    width: 290px;
  }
  @media screen and (min-width: 768px) and (max-width: 992px) {
    width: 420px;
  }
  @media screen and (min-width: 992px) {
    width: 470px;
  }

  &.landscape {
    @media screen and (max-width: 576px) {
      width: 450px;
      max-width: 450px;
    }
    @media screen and (min-width: 576px) and (max-width: 768px) {
      width: 290px;
      max-width: 290px;
    }
    @media screen and (min-width: 768px) and (max-width: 992px) {
      width: 420px;
      max-width: 420px;
    }
    @media screen and (min-width: 992px) {
      width: 470px;
      max-width: 470px;
    }
  }
}
</style>
