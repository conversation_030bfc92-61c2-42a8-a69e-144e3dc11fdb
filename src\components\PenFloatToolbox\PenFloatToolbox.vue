<template>
  <div
      v-show="
        aliveTool === 'PEN' ||
        aliveTool === 'HIGHLIGHT_PEN' ||
        aliveTool === 'LINE' ||
        aliveTool === 'DOTTED_LINE' ||
        aliveTool === 'ERASER'
      "
  >
    <div :class="locationToolsBox">
      <!-- 颜色选择 -->
      <PenColorSelector
          v-show="aliveTool !== 'ERASER'"
          :key="aliveTool"
          :alive-color="aliveColor"
          :alive-tool="aliveTool"
          @change-pen-color="changeAliveColor"
      />
      <!-- 线宽选择 -->
      <PenWidthSlider
          :key="aliveTool"
          :alive-tool="aliveTool"
          :line-width-index="0"
          @change-line-width="changeLineWidth"
      />
      <PenWidthSlider
          :key="aliveTool"
          :alive-tool="aliveTool"
          :line-width-index="1"
          @change-line-width="changeLineWidth"
      />
      <PenWidthSlider
          :key="aliveTool"
          :alive-tool="aliveTool"
          :line-width-index="2"
          @change-line-width="changeLineWidth"
      />
      <!-- 工具栏位置 -->
      <ToolboxPositionSelector @change-toolbox-location="changeToolsBoxLocation"/>
    </div>
  </div>
</template>

<script setup>
import {ref} from "vue";
import PenColorSelector from "@/components/PenFloatToolbox/PenColorSelector.vue";
import PenWidthSlider from "@/components/PenFloatToolbox/PenWidthSlider.vue";
import ToolboxPositionSelector from "@/components/PenFloatToolbox/ToolboxPositionSelector.vue";

defineProps({
  aliveTool: {
    type: String,
    default: ""
  },
  aliveColor: {
    type: String,
    default: "rgb(0, 0, 0)"
  },
});

const emit = defineEmits(["change-pen-color", "change-pen-width"]);

// 工具栏位置 bottom-toolsbox-abslt, top-toolsbox-abslt,topLeft-toolsbox-abslt,topRight-toolsbox-abslt
const locationToolsBox = ref("bottom-toolsbox-abslt");

function changeToolsBoxLocation(value) {
  locationToolsBox.value = value;
}

function changeAliveColor(color) {
  emit("change-pen-color", color);
}

function changeLineWidth(width) {
  emit("change-pen-width", width);
}
</script>

<style scoped>
.bottom-toolsbox-abslt {
  box-shadow: var(--el-box-shadow-dark);
  width: 400px;
  height: 55px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  position: absolute;
  bottom: 50px;
  left: 50%;
  margin-left: -200px;
}

.top-toolsbox-abslt {
  box-shadow: var(--el-box-shadow-dark);
  width: 400px;
  height: 55px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;

  position: absolute;
  top: 70px;
  left: 50%;
  margin-left: -200px;
}

.topLeft-toolsbox-abslt {
  box-shadow: var(--el-box-shadow-dark);
  width: 55px;
  height: 400px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: white;

  position: absolute;
  top: 70px;
  left: 20px;
  /* margin-left: -200px; */
}

.topRight-toolsbox-abslt {
  box-shadow: var(--el-box-shadow-dark);
  width: 55px;
  height: 400px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;

  position: absolute;
  top: 70px;
  right: 20px;
  /* margin-left: -200px; */
}
</style>