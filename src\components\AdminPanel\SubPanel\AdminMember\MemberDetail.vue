<template>
  <el-card v-loading="loading">
    <template #header>
      <h2>{{ memberDetail?.name }}</h2>
      <h3>注册时间: {{ userCreatedAt }}</h3>
      <h3>有效时间：{{ startDate }} - {{ endDate }}</h3>
      <h4>总金额: {{ totalPrice }}</h4>
    </template>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column
        prop="name"
        label="套餐名"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="startDate"
        label="开始时间"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="endDate"
        label="结束时间"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="price" label="付款金额" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { fetchMemberDetails } from "../FetchAdminAPI";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

type MemberDetail = {
  id: string;
  name: string;
  vipStatus: {
    startDate: string;
    endDate: string;
    level: {
      name: string;
    };
    order: {
      price: string;
    };
  }[];
  createdAt: string;
};

const loading = ref(false);
const memberDetail = ref<MemberDetail | null>(null);

const userCreatedAt = computed(() => {
  return memberDetail.value?.createdAt
    ? new Date(memberDetail.value.createdAt).toLocaleString()
    : "";
});
const startDate = computed(() => {
  const vipStatus = memberDetail.value?.vipStatus ?? [];
  if (vipStatus.length === 0) return "";
  const startDate = vipStatus.reduce((prev, cur) => {
    return prev < cur.startDate ? prev : cur.startDate;
  }, vipStatus[0].startDate);
  return startDate ? new Date(startDate).toLocaleString() : "";
});
const endDate = computed(() => {
  const vipStatus = memberDetail.value?.vipStatus ?? [];
  if (vipStatus.length === 0) return "";
  const endDate = vipStatus.reduce((prev, cur) => {
    return prev > cur.endDate ? prev : cur.endDate;
  }, vipStatus[0].endDate);
  return endDate ? new Date(endDate).toLocaleString() : "";
});
const totalPrice = computed(() => {
  return memberDetail.value?.vipStatus.reduce((prev, cur) => {
    return prev + Number(cur.order.price);
  }, 0);
});
const tableData = computed(() => {
  return memberDetail.value?.vipStatus.map((item) => {
    return {
      name: item.level.name,
      startDate: new Date(item.startDate).toLocaleString(),
      endDate: new Date(item.endDate).toLocaleString(),
      price: item.order.price,
    };
  });
});

onMounted(async () => {
  loading.value = true;
  fetchMemberDetails(props.id)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        memberDetail.value = data.data?.member ?? null;
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      loading.value = false;
    });
});
</script>
