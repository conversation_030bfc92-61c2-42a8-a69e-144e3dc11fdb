<template>
  <div>
    <iframe id="bing" ref="bingIframe"
            :src="searchUrl"
            :style="{height: iframeHeight + 'px', 'margin-top': topHeight + 'px'}"
            style="box-sizing: border-box; width: 100%; border: none"
    ></iframe>
  </div>
</template>

<script setup>
import {onMounted, ref, watch} from 'vue'
import {useResizeObserver} from "@vueuse/core";

const props = defineProps({
  keyWord: String
})

const bingIframe = ref(null)
const topHeight = ref(-40) // 新增一个 ref 来存储窗口的高度
const iframeHeight = ref(window.innerHeight - 105 - topHeight.value) // 新增一个 ref 来存储窗口的高度
const searchUrl = ref(`https://m.iciba.com/word?w=${props.keyWord}`)
watch(
    () => props.keyWord,
    () => {
      searchUrl.value = `https://m.iciba.com/word?w=${props.keyWord}`
    }
)

onMounted(() => {
  onResize();
  iframeHeight.value = window.innerHeight - 105 - topHeight.value
})

useResizeObserver(document.body, () => {
  onResize();
  iframeHeight.value = window.innerHeight - 105 - topHeight.value
})

function onResize() {
  if (window.innerWidth > window.innerHeight) {
    topHeight.value = -95
  } else {
    topHeight.value = -70
  }
}
</script>