<template>
  <div>
    <div class="flex flex-wrap gap-2">
      <el-button
        plain
        circle
        @click="selectLetter('')"
        :type="selectedLetter === '' ? 'primary' : ''"
        style="margin-left: 0"
      >
        All
      </el-button>
      <el-button
        plain
        circle
        @click="selectLetter('NUMBER')"
        :type="selectedLetter === 'NUMBER' ? 'primary' : ''"
        style="margin-left: 0"
      >
        数
      </el-button>
      <el-button
        v-for="letter in ALPHABETS"
        :key="letter"
        plain
        circle
        @click="selectLetter(letter)"
        :type="selectedLetter === letter ? 'primary' : ''"
        style="margin-left: 0"
      >
        {{ letter }}
      </el-button>
    </div>
    <div v-loading="loading" style="margin-top: 2rem">
      <el-button
        v-for="item in keywords"
        :key="item.value"
        type="primary"
        plain
        round
        style="margin: 5px"
        @click="handleSelect(item)"
      >
        {{ item.value }}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
// @ts-expect-error import js lib
import { getNoteKeywordsByAlphabet } from "@/lib/RectDatabase.js";

const emits = defineEmits(["select"]);

const ALPHABETS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

const selectedLetter = ref("");
const keywords = ref<{ value: string }[]>([]);
const loading = ref(true);

onMounted(() => {
  querySearch("");
});

const selectLetter = (letter: string) => {
  selectedLetter.value = letter;
  querySearch(letter);
};

const querySearch = async (queryString: string) => {
  loading.value = true;
  keywords.value = await getNoteKeywordsByAlphabet(queryString);
  loading.value = false;
};

const handleSelect = (item: { value: string }) => {
  emits("select", item.value);
};
</script>
<style scoped>
.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-2 {
  gap: 0.5rem;
}
</style>
