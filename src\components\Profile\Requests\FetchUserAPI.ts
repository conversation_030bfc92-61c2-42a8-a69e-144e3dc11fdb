/**
 * 通用GET请求函数
 */
async function fetchGetRequest(url: string) {
  return fetch(
    `${url}${url.includes("?") ? "&" : "?"}t=${new Date().getTime()}`,
    {
      headers: {
        ...getJWTHeaders(),
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
      cache: "no-store",
    }
  ).then((response) => response.json());
}

export async function fetchUserProfile() {
  return fetchGetRequest("/api/user");
}

export async function fetchUserAccounts() {
  return fetchGetRequest("/api/user/accounts");
}

export async function fetchSendSmsCode(phone: string) {
  return fetch("/api/auth/sms", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ phone: phone }),
  }).then((response) => response.json());
}

export async function fetchUserLogin(phone: string, code: string) {
  return fetch("/api/auth/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ phone, code }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.data?.token) {
        localStorage.setItem("jwt_token", data.data.token);
      }
      return data;
    });
}

export async function fetchUserLogout() {
  return fetchGetRequest("/api/user/logout").then((data) => {
    if (data.resCode && data.resCode === "SUCCESS") {
      localStorage.removeItem("jwt_token");
    }
    return data;
  });
}

export async function fetchUserDeleteAuth() {
  return fetchGetRequest("/api/user/accounts/delete-auth");
}

export async function fetchUserDeleteConfirm(code: string) {
  return fetch("/api/user/accounts/delete-confirm", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...getJWTHeaders(),
    },
    body: JSON.stringify({ code }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        localStorage.removeItem("jwt_token");
      }
      return data;
    });
}

export function getJWTHeaders() {
  const token = localStorage.getItem("jwt_token");
  return {
    Authorization: `JWT ${token}`,
  };
}
