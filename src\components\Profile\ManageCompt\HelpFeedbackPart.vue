<template>
  <div class="parent" @click="showHelp">
    <el-text>帮助与支持</el-text>
    <div class="content">
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
  <el-dialog
    v-model="showDialog"
    title="帮助与支持"
    width="90%"
    :append-to-body="true"
  >
    <el-text>
      <h3>
        您可通过如下方法联系客服发起个人信息安全投诉、寻求帮助或者进行咨询
      </h3>
      <el-card shadow="never" style="margin-top: 24px">
        <h4>客服QQ</h4>
        <div @click="copy('3971289193')">
          <el-text type="info"> 3971289193 </el-text>
          <el-icon>
            <CopyDocument />
          </el-icon>
        </div>
        <img
          src="@/components/Profile/Assets/Q二维码.png"
          alt="QQ二维码"
          class="qr-code"
        />
      </el-card>
      <el-card shadow="never" style="margin-top: 24px">
        <h4>技术支持QQ群</h4>
        <div @click="copy('1022565161')">
          <el-text type="info"> 1022565161 </el-text>
          <el-icon>
            <CopyDocument />
          </el-icon>
        </div>
        <img
          src="@/components/Profile/Assets/群二维码.png"
          alt="QR群二维码"
          class="qr-code"
        />
      </el-card>
    </el-text>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const showDialog = ref(false);

function showHelp() {
  showDialog.value = true;
}

function copy(text: string) {
  navigator.clipboard?.writeText(text);
}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}

.qr-code {
  display: block;
  width: 100%;
  max-width: 480px;
  margin: 12px auto;
  border-radius: 4px;
  object-fit: contain;
}
</style>
