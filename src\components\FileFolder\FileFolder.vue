<template>
  <TopHeader />
  <FileTypeTab
    :cardType="cardType"
    @onChangeFileShowType="ChangeFileShowType"
  />
  <FileCardList :key="folderId" :cardType="cardType" />
  <BottomTabBar />
  <FileAdd />
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
// @ts-expect-error import js lib
import { useAppStore } from "@/stores/app";
// @ts-expect-error import js lib
import { getFileMeta, putFileMeta } from "@/lib/FileList.js";
// @ts-expect-error import js lib
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";
// @ts-expect-error import js lib
import FileAdd from "@/components/FileFolder/FileAdd.vue";
// @ts-expect-error import js lib
import TopHeader from "@/components/FileFolder/TopHeader.vue";
// @ts-expect-error import js lib
import FileTypeTab from "@/components/FileFolder/FileTypeTab.vue";
// @ts-expect-error import js lib
import FileCardList from "@/components/FileFolder/FileCardList.vue";
// @ts-expect-error import js lib
import { showAskFilenameDialog } from "@/components/FileFolder/Helper/AskFilenameDialog.jsx";

const cardType = ref("card");
const router = useRouter();
const appStore = useAppStore();
const folderId = computed(() => router.currentRoute.value.query.folder ?? "");

onMounted(() => {
  changeFilenameAtFirstTime();
});

async function changeFilenameAtFirstTime() {
  if (!appStore.currentFileId) {
    return;
  }

  const fileMeta = await getFileMeta(appStore.currentFileId);
  if (!fileMeta || !fileMeta.isNew) {
    return;
  }

  showAskFilenameDialog("重命名", fileMeta.name)
    .then((newName: string) => {
      if (newName) {
        fileMeta.name = newName;
      }
    })
    .finally(() => {
      fileMeta.isNew = false;
      putFileMeta(fileMeta);
    });
}

function ChangeFileShowType() {
  cardType.value = cardType.value === "card" ? "strip" : "card";
}
</script>

<style scoped></style>
