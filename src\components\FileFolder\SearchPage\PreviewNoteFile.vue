<!-- 根据关键词搜索到相关笔记 点击笔记列表 弹出预览查看笔记上下文 -->
<template>
    <van-overlay :show="show" z-index="100">
        <div class="wrapper">
            <div class="block">
                <div class="corner-div" @click="close">
                    <van-icon name="clear" size="40" color="#c8c9cc" />
                </div>
                <div class="centerFile-div">
                    <!-- 嵌入预览选择的笔记文件，定位到笔记位置，只查看 -->
                    <iframe style="width: 100%;height: 100%;" src="https://mozilla.github.io/pdf.js/web/viewer.html"
                        id="mobsf2" scrolling="no" frameborder="0">
                    </iframe>
                </div>
                <div @click="open" class="bottom-div">
                    <h4 style="color: #409EFF;">打开笔记<van-icon name="arrow" /></h4>
                </div>
            </div>
        </div>
    </van-overlay>
</template>

<script setup>
import { ref } from 'vue';

const show = ref(false);
const file = ref(null);
const note = ref(null);
const close = () => {
    console.log('close');
    show.value = false;
}
const showPreview = (selectedFile, clickNote) => {
    file.value = selectedFile;
    note.value = clickNote;
    show.value = true;
}
const open = () => {
    console.log('切换到预览的笔记');
    show.value = false;
}
defineExpose({
    showPreview
})

</script>

<style scoped>
.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.block {
    width: calc(100vw - 50px);
    height: calc(100vh - 50px);
    border-radius: 8px;
    background-color: #fff;
    position: relative;
}

.corner-div {
    position: absolute;
    top: -20px;
    right: -20px;
    z-index: 999;
}

.bottom-div {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 999;
    height: 50px;
    text-align: right;
    line-height: 50px;
    padding-right: 20px;
}

.centerFile-div {
    display: flex;
    justify-content: center;
    height: calc(100% - 50px);
}
</style>
