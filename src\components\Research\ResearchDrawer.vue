<!-- Component: ResearchDrawer 文档中选中文字 点研究按钮弹出的抽屉 -->
<template>
  <el-drawer
    v-model="drawer"
    :with-header="false"
    :destroy-on-close="true"
    size="70%"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="翻译" name="translate">
        <ResearchTranslate :keyWord="keyWordString" />
      </el-tab-pane>
      <el-tab-pane label="搜索" name="searchNet">
        <ResearchNet :keyWord="keyWordString" />
      </el-tab-pane>
      <el-tab-pane label="相关笔记" name="relatedNotes">
        <ResearchNoteList :keyWord="keyWordString" />
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script setup>
import { ref } from "vue";
import { useEventListener } from "vue-hooks-plus";
import {
  createNoteFromRect,
  getNoteFromRect,
  saveNote,
} from "@/lib/RectDatabase.js";
import { useOCRStore } from "@/stores/ocr.js";
import { LayerType, RECT_ACTION_TYPE } from "@/constant.js";
import ResearchNoteList from "@/components/Research/ResearchNoteList.vue";
import ResearchNet from "@/components/Research/ResearchNet.vue";
import ResearchTranslate from "@/components/Research/ResearchTranslate.vue";

useEventListener("message", receiveMessageFromChild);
const ocrStore = useOCRStore();

function receiveMessageFromChild(event) {
  const { layerType, type, rect, text } = event.data;
  if (layerType === LayerType.RECT && type === RECT_ACTION_TYPE.RESEARCH) {
    if (rect) {
      getOCRContent(rect);
    } else if (text) {
      showDrawer(text);
    }
  }
}

const activeName = ref("translate");

const drawer = ref(false);
const keyWordString = ref("");

async function getOCRContent(rect) {
  if (!rect || !rect.dataUrl) {
    return;
  }

  const uid = rect.uid;
  let noteContent = await getNoteFromRect(uid);
  if (!noteContent) {
    noteContent = createNoteFromRect(rect.fileId, rect.uid);
  }

  if (noteContent?.ocrContent) {
    showDrawer(noteContent.ocrContent);
    return;
  }

  // ocr识别
  wasmGetText(rect.dataUrl).then((resultText) => {
    noteContent.ocrContent = resultText;
    const ocrNoteContent = {
      uid: Date.now(),
      type: "TEXT",
      value: resultText,
      isContent: true,
    };
    noteContent.noteContentArray = [
      ocrNoteContent,
      ...noteContent.noteContentArray,
    ];

    showDrawer(noteContent.ocrContent);

    saveNote(noteContent);
  });
}

async function wasmGetText(imageBase64) {
  const imageResponse = await fetch(imageBase64);
  const imageBlob = await imageResponse.blob();
  const image = await createImageBitmap(imageBlob);

  await ocrStore.ocrWorker?.loadImage(image);
  return await ocrStore.ocrWorker?.getText();
}

const showDrawer = (keyword) => {
  drawer.value = true;
  keyWordString.value = keyword;
};
</script>
