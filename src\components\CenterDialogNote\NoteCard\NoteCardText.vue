<template>
  <div ref="noteCardTextRef" class="text-container">
    <div v-if="showInput && cardEdit" style="width: 100%">
      <InputTextArea
        ref="input"
        :text="itemValue"
        :auto-focus="needAutoFocus"
        @text-changed="onTextChanged"
        @on-focus="onFocus"
      />
    </div>
    <div v-else class="text-wrapper">
      <StretchedLink v-if="cardEdit" @click="changeShowInput" />
      <el-text v-if="item.value" tag="p" class="text">
        {{ item.value }}
      </el-text>
      <el-text v-else-if="cardEdit" tag="p" type="info"> 添加内容... </el-text>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from "vue";
import StretchedLink from "@/components/NotebookPanel/NoteCardCompt/StretchedLink.vue";
import InputTextArea from "@/components/BaseComponents/InputTextArea.vue";

const emits = defineEmits(["update-item"]);
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  cardEdit: {
    type: Boolean,
    required: true,
  },
});

const noteCardTextRef = ref(null);
const showInput = ref(false);
const input = ref(null);
const itemValue = ref(props.item.value);
const needAutoFocus = ref(props.item.isNew);

onMounted(() => {
  if (props.cardEdit && props.item && !props.item.value && props.item.isNew) {
    nextTick(() => {
      scrollToComponent();
      changeShowInput();
    });
  }
});

function scrollToComponent() {
  if (noteCardTextRef.value) {
    noteCardTextRef.value?.scrollIntoView({ behavior: "smooth", block: "end" });
  }
}

function changeShowInput() {
  showInput.value = true;
}

function onTextChanged(value) {
  itemValue.value = value;
  showInput.value = false;
  emits("update-item", { ...props.item, value: itemValue.value });
}

function onFocus() {
  needAutoFocus.value = false;
  emits("update-item", { ...props.item, isNew: false });
}
</script>

<style scoped>
.text-container {
  /* background-color: yellow; */
  display: flex;
  /* min-height: 32px; */
  /* padding-left: 16px; */
}

.text-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-wrapper .text {
  white-space: pre-wrap;
}
</style>
