<template>
  <el-main v-loading="loading">
    <VersionInfo :show-back="true" title="登录方式管理" />
    <el-row justify="center" style="margin-top: 20px">
      <el-col :sm="16" :lg="12">
        <el-card shadow="never">
          <el-row v-if="phoneAccount">
            <el-col>
              <PhoneLogin :account="phoneAccount" />
              <el-divider style="margin: 12px 0" />
            </el-col>
          </el-row>
          <el-row v-if="wechatAccount">
            <el-col>
              <WechatLogin />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <div style="margin-bottom: 50px"></div>
    <BottomTabBar active="profile" />
  </el-main>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import VersionInfo from "@/components/Profile/VersionInfo.vue";
import PhoneLogin from "@/components/Profile/LoginMethodCompt/PhoneLogin.vue";
import WechatLogin from "@/components/Profile/LoginMethodCompt/WechatLogin.vue";
// @ts-expect-error 导入js组件
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";
import { fetchUserAccounts } from "./Requests/FetchUserAPI";

type subAccount = {
  type: string;
  provider: string;
  providerAccountId: string;
  placeholder: string;
};

const loginAccounts = ref<subAccount[]>([]);
const loading = ref(false);

const phoneAccount = computed(() => {
  return loginAccounts.value.find((account) => account.type === "sms");
});

const wechatAccount = computed(() => {
  return null;
});

onMounted(() => {
  loading.value = true;
  fetchUserAccounts()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        loginAccounts.value = data.data?.accounts;
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("获取用户账户信息失败");
      }
    })
    .catch(() => {
      ElMessage.error("获取用户账户信息失败");
    })
    .finally(() => {
      loading.value = false;
    });
});
</script>
