import {defineStore} from "pinia";
import {ref} from "vue";

export const useNotePenStore = defineStore("note-pen", () => {
    const isNotePenToolShow = ref(false); // 笔记工具栏是否显示
    const aliveTool = ref("PEN"); // 当前工具
    const aliveColor = ref("rgb(0, 0, 0)"); // 当前工具 使用中颜色
    const aliveLineWidth = ref(10); // 当前工具 使用中线宽高

    return {
        isNotePenToolShow,
        aliveTool,
        aliveColor,
        aliveLineWidth
    }
})