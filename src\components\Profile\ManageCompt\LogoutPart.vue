<template>
  <el-button type="danger" style="width: 100%" @click="doLogout">
    退出登录
  </el-button>
</template>

<script lang="ts" setup>
import { ElLoading, ElMessage } from "element-plus";
import { fetchUserLogout } from "@/components/Profile/Requests/FetchUserAPI";

const emits = defineEmits(["logout"]);

function doLogout() {
  const loading = ElLoading.service({
    lock: true,
  });
  fetchUserLogout()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("退出登录成功");
        emits("logout");
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("退出登录失败");
      }
    })
    .finally(() => loading.close());
}
</script>
