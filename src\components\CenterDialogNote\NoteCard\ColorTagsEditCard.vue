<template>
  <el-card
    class="tag-card"
    shadow="never"
    :body-style="{ padding: '5px 10px 5px 10px' }"
  >
    <div class="card-flex">
      <div class="align-center-start">
        <span style="margin-right: 10px"># {{ tag.name }}</span>
        <el-button size="small" circle :color="tag.color" @click="editColor" />
      </div>
      <div>
        <el-button link type="danger" @click="onDelete">
          <el-icon><Delete /> </el-icon>
        </el-button>
        <el-button link type="primary" @click="onEdit">
          <el-icon> <Edit /> </el-icon>
        </el-button>
      </div>
    </div>
  </el-card>
  <el-dialog
    v-model="editDialogVisible"
    title="编辑标签"
    width="30%"
    :destroy-on-close="true"
    :style="{ minWidth: '350px' }"
  >
    <el-input
      class="tag-input"
      v-model="inputValue"
      placeholder="请输入标签名"
      clearable
      autofocus
    />
    <div style="margin-top: 10px; text-align: center">
      <el-button type="success" @click="onEditFinish">确定</el-button>
      <el-button type="danger" @click="onCancelEdit">取消</el-button>
    </div>
  </el-dialog>
  <el-drawer
    v-model="showDrawer"
    title="标签自定义"
    :append-to-body="true"
    :destroy-on-close="true"
    :size="isVertical ? '70%' : drawerLandscapeWidth"
    :direction="isVertical ? 'btt' : 'rtl'"
  >
    <!-- 选择颜色 -->
    <ColorSelect
      :aliveColor="tag.color"
      :currentTool="CURRENT_TOOL"
      @color-changed="changeTagColor"
    />
  </el-drawer>
</template>

<script setup>
import { computed, ref } from "vue";
import { useWindowSize } from "@vueuse/core";
import ColorSelect from "@/components/ToolSelection/ColorSelect.vue";

const props = defineProps({
  tag: {
    type: Object,
    required: true,
  },
});

const emits = defineEmits(["delete-tag", "edit-finish"]);
const CURRENT_TOOL = { type: "PEN" };

const { width, height } = useWindowSize();
const isVertical = computed(() => width.value > height.value);
const drawerLandscapeWidth = computed(() =>
  width.value < 900 ? "300px" : "30%"
);

const editDialogVisible = ref(false);
const showDrawer = ref(false);
const inputValue = ref("");

const onDelete = () => {
  emits("delete-tag", props.tag);
};

const onEdit = () => {
  inputValue.value = props.tag.name;
  editDialogVisible.value = true;
};

function editColor() {
  showDrawer.value = true;
}

function changeTagColor(color) {
  emits("edit-finish", { ...props.tag, color });
}

function onEditFinish() {
  editDialogVisible.value = false;
  emits("edit-finish", { ...props.tag, name: inputValue.value });
}

function onCancelEdit() {
  editDialogVisible.value = false;
}
</script>
<style scoped>
.tag-card {
  margin-bottom: 8px;
}

.no-border {
  border: none !important;
}

.card-flex {
  display: flex;
  justify-content: space-between;
}

.align-center-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.align-center-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.tag-input {
  width: 100%;
}
</style>
