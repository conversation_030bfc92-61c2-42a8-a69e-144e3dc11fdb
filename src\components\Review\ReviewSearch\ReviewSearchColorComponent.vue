<template>
  <div style="margin-left: 10px; margin-right: 10px">
    <div class="btn-wrapper">
      <div v-for="colorTag in colorTags" :key="colorTag.id" class="tag-outer">
        <van-tag
          :color="colorTag.color"
          size="large"
          :plain="!isTagActive(colorTag)"
          @click="changeActiveColorTag(colorTag)"
        >
          {{ colorTag.name }}
        </van-tag>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { listColorTags } from "@/lib/RectDatabase";
import { ColorTags as DefaultColorTags } from "@/components/CenterDialogNote/NoteCard/DefaultValues";

const emits = defineEmits(["on-search"]);

const colorTags = ref([]);
const activeColorTag = ref(null);

onMounted(() => {
  loadColorTags();
});

async function loadColorTags() {
  const list = await listColorTags();
  if (list.length === 0) {
    colorTags.value = DefaultColorTags;
  } else {
    colorTags.value = list;
  }
}

function isTagActive(colorTag) {
  return activeColorTag.value?.id === colorTag.id;
}

function changeActiveColorTag(colorTag) {
  if (isTagActive(colorTag)) {
    activeColorTag.value = null;
  } else {
    activeColorTag.value = colorTag;
  }

  emits("on-search", activeColorTag.value);
}
</script>

<style scoped>
.btn-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: left;
  align-items: center;
}
.tag-outer {
  margin: 5px;
}
</style>
