<template>
  <div v-if="userProfile" class="center-item">
    <el-avatar :size="48" :icon="UserFilled" />
    <h3 class="profile-name">
      {{ username }}
    </h3>
    <div v-if="!hideVIPFeature" class="end-date">
      <el-text v-if="isVIP" type="primary">{{ endDate }}</el-text>
      <el-text v-else type="info">未开通会员</el-text>
    </div>
  </div>
  <div v-else class="center-item">
    <el-avatar :size="48" :icon="UserFilled" />
    <el-button size="large" text style="padding: 20px" @click="gotoLoginPage">
      <h2><strong>登 录</strong></h2>
    </el-button>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { UserFilled } from "@element-plus/icons-vue";
import { useProfileStore } from "@/stores/profile";

const props = defineProps({
  emitLogin: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["login"]);

const router = useRouter();
const profileStore = useProfileStore();
const userProfile = computed(() => profileStore.userProfile);
const username = computed(() => profileStore.userProfile?.user?.name);
const isVIP = computed(() => profileStore.isVIP);
const hideVIPFeature = computed(() => profileStore.hideVIPFeature);

const endDate = computed(() => {
  if (profileStore.isVIP) {
    return dayjs(profileStore.userProfile?.vip?.endDate).format("YYYY-MM-DD");
  }
  return "";
});

function gotoLoginPage() {
  if (props.emitLogin) {
    emits("login");
  } else {
    router.push({ path: "/login" });
  }
}
</script>

<style lang="scss" scoped>
.center-item {
  display: flex;
  align-items: center;

  .end-date {
    margin-left: auto;
  }
}

.profile-name {
  display: inline-block;
  margin-left: 1rem;
}
</style>
