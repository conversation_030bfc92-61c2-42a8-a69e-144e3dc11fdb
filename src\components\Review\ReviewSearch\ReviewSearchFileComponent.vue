<template>
    <div style="margin-left: 10px;margin-right: 10px;margin-top: 10px;padding-bottom: 50px">
        <van-checkbox-group v-model="checked" shape="square" @change="handleChange">
            <van-checkbox v-for="(file, index) in fileList" :key="index" :name="file.name" style="margin: 8px;">
                {{ file.name }}
            </van-checkbox>
        </van-checkbox-group>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const checked = ref([]);
const handleChange = (value) => {
    console.log(value);
    console.log(checked.value);
}
let fileList = [
  { "id": 1, "name": "Article 1" },
  { "id": 2, "name": "Article 2" },
  { "id": 3, "name": "Article 3" },
  { "id": 4, "name": "Article 4" },
  { "id": 5, "name": "Article 5" },
  { "id": 6, "name": "Article 6" },
  { "id": 7, "name": "Article 7" },
  { "id": 8, "name": "Article 8" },
  { "id": 9, "name": "Article 9" },
  { "id": 10, "name": "Article 10" },
  { "id": 11, "name": "Article 11" },
  { "id": 12, "name": "Article 12" },
  { "id": 13, "name": "Article 13" },
  { "id": 14, "name": "Article 14" },
  { "id": 15, "name": "Article 15" },
  { "id": 16, "name": "Article 16" },
  { "id": 17, "name": "Article 17" },
  { "id": 18, "name": "Article 18" },
  { "id": 19, "name": "Article 19" },
  { "id": 20, "name": "Article 20" },
  { "id": 21, "name": "Article 21" },
  { "id": 22, "name": "Article 22" },
  { "id": 23, "name": "Article 23" },
  { "id": 24, "name": "Article 24" },
  { "id": 25, "name": "Article 25" },
  { "id": 26, "name": "Article 26" },
  { "id": 27, "name": "Article 27" },
  { "id": 28, "name": "Article 28" },
  { "id": 29, "name": "Article 29" },
  { "id": 30, "name": "Article 30" },
  { "id": 31, "name": "Article 31" },
  { "id": 32, "name": "Article 32" },
  { "id": 33, "name": "Article 33" },
  { "id": 34, "name": "Article 34" },
  { "id": 35, "name": "Article 35" },
  { "id": 36, "name": "Article 36" },
  
];

</script>
<style scoped></style>
 