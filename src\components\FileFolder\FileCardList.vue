<template>
  <el-container>
    <el-main style="padding: 10px">
      <div
        v-if="!fileFolderStore.firstFileListQueried"
        style="height: 300px"
        v-loading="true"
      ></div>
      <el-row v-else-if="cardType === 'card'">
        <el-col
          v-if="parentId || parentId === ''"
          :lg="4"
          :md="4"
          :sm="6"
          :xl="3"
          :xs="8"
        >
          <div
            style="
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100%;
            "
            @click="enterFolder(parentId)"
          >
            <el-icon :size="128"><Back /></el-icon>
          </div>
        </el-col>
        <el-col
          v-for="file in showingFileList"
          :key="file.id"
          :lg="4"
          :md="4"
          :sm="6"
          :xl="3"
          :xs="8"
        >
          <FileCard :file="file" @on-file-click="onFileClick" />
        </el-col>
      </el-row>
      <!-- todo: 列表显示 -->
      <el-row v-else>
        <el-col v-if="parentId || parentId === ''">
          <div
            style="
              display: flex;
              justify-content: start;
              align-items: center;
              height: 100%;
            "
            @click="enterFolder(parentId)"
          >
            <el-icon :size="48"><Back /></el-icon>
          </div>
        </el-col>
        <el-col v-for="file in showingFileList" :key="file.id">
          <FileCard :file="file" @on-file-click="onFileClick" />
        </el-col>
      </el-row>
    </el-main>
  </el-container>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useEventListener } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { Back } from "@element-plus/icons-vue";
import { useAppStore } from "@/stores/app.js";
import { useFileFolderStore } from "@/stores/file-folder";
import { getFileMeta } from "@/lib/FileList.js";
import FileCard from "@/components/FileFolder/FileCard.vue";
// import FileCardStrip from "@/components/FileFolder/FileCardStrip.vue";

defineProps({
  cardType: {
    type: String,
  },
});

const router = useRouter();
const appStore = useAppStore();
const fileFolderStore = useFileFolderStore();

const folderId = router.currentRoute.value.query.folder;
const parentId = router.currentRoute.value.query.parent;

const showingFileList = computed(() => {
  const list = fileFolderStore.fileList;

  if (fileFolderStore.searchKeyword) {
    return list.filter((f) => {
      return (
        f.name.includes(fileFolderStore.searchKeyword) && f.type === "file"
      );
    });
  } else {
    return list.filter((f) => {
      if (folderId) {
        return f.parentId === Number(folderId);
      } else {
        return !f.parentId;
      }
    });
  }
});

function onFileClick(file) {
  if (file.type === "folder") {
    enterFolder(file.id);
  } else if (file.type === "file") {
    openFile(file.id);
  }
}

async function enterFolder(folderId) {
  if (folderId === "") {
    appStore.currentFolderMeta = null;
    appStore.lastVisitedFolderId = 0;
    await router.replace({
      path: "/file",
    });
    return;
  }

  const folder = await getFileMeta(folderId);
  if (!folder) {
    ElMessage.error("文件夹不存在");
    return;
  }
  appStore.currentFolderMeta = folder;
  appStore.lastVisitedFolderId = folder.id;
  await router.replace({
    path: "/file",
    query: {
      folder: folder.id,
      parent: folder.parentId,
    },
  });
}

async function openFile(fileId) {
  const fileMeta = await getFileMeta(fileId);
  if (!fileMeta) {
    ElMessage.error("文件不存在");
    return;
  }
  appStore.currentFileMeta = fileMeta;
  if (typeof ReactNativeWebView !== "undefined") {
    const data = {
      action: "set-target-file-id-confirm",
      timestamp: fileMeta.id.toString(),
    };
    // eslint-disable-next-line no-undef
    ReactNativeWebView.postMessage(JSON.stringify(data));
  }
}

useEventListener("message", (event) => {
  if (event?.data?.action === "expo-open-file-confirm") {
    const { fileId } = event.data;

    if (fileId) {
      appStore.currentFile = null;
      router.replace("/app");
    } else {
      ElMessage.error("文件不存在");
    }
  }
});
</script>
<style scoped></style>
