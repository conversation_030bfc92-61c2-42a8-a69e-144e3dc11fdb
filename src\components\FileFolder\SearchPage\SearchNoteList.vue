<template>
  <div style="margin-left: 10px; margin-right: 10px; margin-top: 0;">
    <el-scrollbar ref="scrollbar" :style="{ height: scrollbarHeight - 100 + 'px' }">
      <div style="background-color: #f4f4f5; padding-bottom: 20px;">
        <van-cell-group v-for="item in noteList" :key="item.fileId" inset>
          <template #title>
            <div class="custom-title">{{ item.name }}</div>
          </template>
          <van-cell v-for="note in item.notes" :key="note.uid" :label="note.value"
                    :title="note.title || trunkString(note.value)"
                    @click="openFile(item)"/>
        </van-cell-group>
      </div>
      <!-- <div style="margin-top: 10px; margin-bottom: 10px;">
        <h4 style="text-align: center; color: #409EFF;">加载更多</h4>
      </div> -->
    </el-scrollbar>
  </div>
  <PreviewNoteFile ref="previewNoteFileRef"/>
</template>

<script setup>
import {onMounted, ref} from 'vue';
import {searchNotesByKeyword} from "@/lib/NoteList.js";
import PreviewNoteFile from "@/components/FileFolder/SearchPage/PreviewNoteFile.vue";

const props = defineProps({
  keyWord: {
    type: String,
    default: ''
  }
})
const previewNoteFileRef = ref(null);

const scrollbar = ref(null);
const scrollbarHeight = ref(0);
const noteList = ref([])

onMounted(() => {
  const scrollbarY = scrollbar.value.$el.getBoundingClientRect().top;
  const viewportHeight = window.innerHeight;
  scrollbarHeight.value = viewportHeight - scrollbarY;
});

onMounted(() => {
  searchNotesByKeyword(props.keyWord)
      .then(list => {
        // merge notes with same fileId to sub array
        const groupedList = [];
        list.forEach(note => {
          const index = groupedList.findIndex(item => item.fileId === note.fileId);
          if (index !== -1) {
            groupedList[index].notes.push(...expandNotes(note));
          } else {
            groupedList.push({
              fileId: note.fileId,
              name: note.name,
              notes: [...expandNotes(note)]
            });
          }
        });
        noteList.value = groupedList;
      })
})

function expandNotes(note) {
  const list = note.notes || [];

  return list.filter(item => item.type === "TEXT");
}

function trunkString(string) {
  // string max length = 32
  return string.length > 32 ? string.substring(0, 32) + '...' : string;
}

const openFile = (file) => {
  previewNoteFileRef.value?.showPreview(file);
}

</script>

<style scoped>
.custom-title {
  color: #000000;
  font-size: 15px;
}
</style>