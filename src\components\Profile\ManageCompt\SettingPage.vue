<template>
  <div class="parent" @click="gotoSetting">
    <el-text>设置</el-text>
    <div class="content">
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router";

const router = useRouter();

const gotoSetting = () => {
  router.push("/profile/setting");
};
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
