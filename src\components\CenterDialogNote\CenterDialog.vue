<template>
  <!-- 选中单词 中间弹窗 笔记 翻译 划线 -->
  <DialogNoModal
    v-if="dialogOverflowVisible"
    v-model="dialogOverflowVisible"
    class="dialog-no-modal"
    :class="dialogClasses"
    :style="dialogStyles"
    :dialog-location="dialogLocation"
    :draggable="!dialogFullscreen"
    overflow
    :lock-scroll="false"
    :modal="false"
    :close-on-click-modal="false"
  >
    <div
      style="
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        pointer-events: none;
      "
    >
      <div style="pointer-events: auto">
        <div v-if="isNewRect">
          <el-button type="success" size="small" @click="closeModal">
            保存
          </el-button>
          <el-button type="danger" size="small" @click="sendDeleteMessage">
            取消
          </el-button>
        </div>
        <el-button v-else link @click="closeModal">
          <el-icon :size="20"><CloseBold /></el-icon>
        </el-button>
      </div>
      <div style="pointer-events: auto">
        <el-button v-if="currentRect" link type="primary" @click="aboutNotes">
          相关笔记
        </el-button>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        label="笔记"
        name="RECT_Note"
        :lazy="true"
        style="height: 100%"
      >
        <div
          v-if="currentRect"
          class="note-container"
          :class="{ landscape: dialogLandscape }"
        >
          <div>
            <el-scrollbar ref="noteScrollbar" :max-height="scrollbarMaxHeight">
              <NoteCardOuter
                v-if="teleportReady"
                :key="currentRect.uid"
                :rect="currentRect"
                :card-edit="true"
                :fullscreen="dialogFullscreen"
                :is-landscape="dialogLandscape"
                :is-new="isNewRect"
                @toggle-fullscreen="toggleFullScreen"
                @delete-item="sendDeleteMessage"
              />
            </el-scrollbar>
            <div
              v-show="dialogLandscape"
              ref="teleportFixedBottomLandscapeRef"
              class="fixed-bottom"
              :id="`rbl-${currentRect.uid}`"
            ></div>
          </div>
          <el-divider
            v-show="dialogLandscape"
            direction="vertical"
            style="height: 100%"
          />
          <el-scrollbar
            v-show="dialogLandscape"
            ref="drawingScrollbar"
            :max-height="scrollbarMaxHeight"
          >
            <div ref="teleportContainerRef" :id="`r-${currentRect.uid}`"></div>
          </el-scrollbar>
          <div
            v-show="!dialogLandscape"
            ref="teleportFixedBottomVerticalRef"
            class="fixed-bottom"
            :id="`rbv-${currentRect.uid}`"
          ></div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="翻译" name="RECT_RESEARCH" :lazy="true">
        <TransFrame
          v-if="translateKeyWord"
          :keyWord="translateKeyWord"
          :fullscreen="dialogFullscreen"
          :landscape="dialogLandscape"
        />
      </el-tab-pane>
    </el-tabs>
  </DialogNoModal>
</template>

<script setup>
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { useEventListener, useResizeObserver } from "@vueuse/core";
import { LayerType, RECT_ACTION_TYPE } from "@/constant";
import { useAppStore } from "@/stores/app.js";
import NoteCardOuter from "@/components/CenterDialogNote/NoteCardOuterDlg.vue";
import DialogNoModal from "@/components/BaseComponents/DialogNoModal.vue";
import TransFrame from "@/components/CenterDialogNote/Trans/TransFrame.vue";

defineProps({
  dialogLocation: {
    type: String,
  },
});

const appStore = useAppStore();
const dialogFullscreen = ref(false);
const dialogLandscape = ref(false);
const dialogOverflowVisible = ref(false);
const activeName = ref("RECT_RESEARCH"); //tab默认选中
const teleportReady = ref(false);
const noteScrollbar = ref(null);
const drawingScrollbar = ref(null);
const teleportContainerRef = ref(null);
const teleportFixedBottomLandscapeRef = ref(null);
const teleportFixedBottomVerticalRef = ref(null);
const currentRect = ref(null);
const isNewRect = ref(false);
const translateKeyWord = ref("");
const translateUid = ref(0);
const emit = defineEmits(["change-right-panel"]);

watch(
  () => [
    teleportFixedBottomLandscapeRef.value,
    teleportFixedBottomVerticalRef.value,
    teleportContainerRef.value,
  ],
  () => {
    nextTick(() => {
      teleportReady.value =
        !!teleportFixedBottomLandscapeRef.value &&
        !!teleportFixedBottomVerticalRef.value &&
        !!teleportContainerRef.value;
    });
  }
);

onMounted(() => {
  dialogLandscape.value =
    window.screen.width > window.screen.height ||
    appStore.deviceType === "tablet";
});
useResizeObserver(document.body, (entries) => {
  const entry = entries[0];
  dialogLandscape.value =
    entry?.contentRect?.width > entry?.contentRect?.height ||
    appStore.deviceType === "tablet";
});

watch(
  () => activeName.value,
  (val) => {
    if (val === "RECT_Note" && !currentRect.value && translateUid.value) {
      const iframe = document.getElementById("mobsf4");
      if (iframe) {
        const data = {
          layerType: LayerType.RECT,
          type: RECT_ACTION_TYPE.ADD_NOTE,
          uid: translateUid.value,
        };
        iframe.contentWindow.postMessage(data, "*");
      }
    }
  }
);

watch(
  [dialogFullscreen, dialogLandscape],
  (
    [newDialogFullscreen, newDialogLandscape],
    [oldDialogFullscreen, oldDialogLandscape]
  ) => {
    if (
      (!oldDialogFullscreen && newDialogFullscreen) ||
      (!oldDialogLandscape && newDialogLandscape)
    ) {
      nextTick(() => {
        noteScrollbar.value?.setScrollTop(0);
        drawingScrollbar.value?.setScrollTop(0);
      });
    }
  }
);

const dialogClasses = computed(() => {
  return {
    fullscreen: dialogFullscreen.value,
    landscape: dialogLandscape.value,
  };
});

// 全屏时强制重置位置居中
const dialogStyles = computed(() => {
  return dialogFullscreen.value
    ? {
        left: "50%",
        top: "50%",
      }
    : {};
});

const scrollbarMaxHeight = computed(() => {
  return dialogFullscreen.value ? "calc(100vh - 133px)" : "calc(75vh - 133px)";
});

useEventListener("message", receiveMessageFromChild);
function receiveMessageFromChild(event) {
  const { layerType, type, rect, text, uid, isNew } = event.data;
  if (layerType === LayerType.RECT && type === RECT_ACTION_TYPE.RESEARCH) {
    // 翻译
    isNewRect.value = false;
    if (text) {
      translateKeyWord.value = text;
      translateUid.value = uid;
      currentRect.value = null;
      dialogOverflowVisible.value = true;
      changeDialogFullscreenByDevice();
      activeName.value = "RECT_RESEARCH";
    }
  } else if (
    layerType === LayerType.RECT &&
    type === RECT_ACTION_TYPE.DISPLAY
  ) {
    //笔记
    currentRect.value = rect;
    isNewRect.value = !!isNew;
    if (text) {
      translateKeyWord.value = text;
      translateUid.value = 0;
    }
    dialogOverflowVisible.value = true;
    changeDialogFullscreenByDevice();
    activeName.value = "RECT_Note";
  } else if (layerType === LayerType.RECT && type === RECT_ACTION_TYPE.DELETE) {
    currentRect.value = null;
    translateKeyWord.value = "";
    translateUid.value = 0;
    dialogOverflowVisible.value = false;
    changeDialogFullscreenByDevice();
  }
}

function changeDialogFullscreenByDevice() {
  dialogFullscreen.value = appStore.isMobile;
}

function aboutNotes() {
  appStore.currentSearchingRect = currentRect.value;
  changeDialogFullscreenByDevice();
  nextTick(() => {
    const value = dialogFullscreen.value ? "fullscreen" : "";
    emit("change-right-panel", value);
  });
}

function closeModal() {
  dialogOverflowVisible.value = false;
  dialogFullscreen.value = false;
}

function toggleFullScreen(value) {
  dialogFullscreen.value = value;
}

function sendDeleteMessage() {
  // todo: post message by store action
  const iframe = document.getElementById("mobsf4");
  if (iframe) {
    const data = {
      layerType: LayerType.RECT,
      type: RECT_ACTION_TYPE.DELETE,
      uid: currentRect.value.uid,
    };
    iframe.contentWindow.postMessage(data, "*");
  }
}
</script>

<style scoped>
.tab-extra-button {
  position: absolute;
  right: 20px;
  top: 15px;
}
</style>

<style lang="scss" scoped>
.dialog-no-modal {
  max-height: 75vh;
  min-height: calc(75vh - 101px);
  background-color: #fdfaf3;
  border-radius: 10px;
  display: grid;
  grid-template-rows: auto 1fr;

  @media screen and (max-width: 576px) {
    width: 300px;
    max-width: 300px;
  }
  @media screen and (min-width: 576px) and (max-width: 768px) {
    width: 320px;
    max-width: 320px;
  }
  @media screen and (min-width: 768px) and (max-width: 992px) {
    width: 450px;
    max-width: 450px;
  }
  @media screen and (min-width: 992px) {
    width: 500px;
    max-width: 500px;
  }

  &.fullscreen {
    width: 100% !important;
    height: 100%;
    max-height: 100%;
    max-width: 100% !important;
  }

  &.landscape {
    @media screen and (max-width: 576px) {
      width: 480px;
      max-width: 480px;
    }
    @media screen and (min-width: 576px) and (max-width: 768px) {
      width: 320px;
      max-width: 320px;
    }
    @media screen and (min-width: 768px) and (max-width: 992px) {
      width: 450px;
      max-width: 450px;
    }
    @media screen and (min-width: 992px) {
      width: 500px;
      max-width: 500px;
    }
  }
}

.note-container {
  height: 100%;
  display: grid;
  grid-template-rows: 1fr auto; /* 上层占用剩余空间，下层自适应 */
  grid-template-columns: minmax(0, 1fr);

  &.landscape {
    grid-template-columns: 40% 17px calc(60% - 17px);
    grid-template-rows: auto;
    grid-auto-flow: column;

    > div:first-child {
      height: 100%;
      display: grid;
      grid-template-rows: 1fr auto; /* 上层占用剩余空间，下层自适应 */
    }
  }
}

.fixed-bottom {
  display: flex;
  align-items: center;
  justify-content: end;
}
</style>
