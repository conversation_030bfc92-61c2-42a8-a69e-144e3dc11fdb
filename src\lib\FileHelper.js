import * as mimeTypes from "mime-types";

export function base64ToFile(base64String, filename) {
  if (!base64String) {
    return undefined;
  }
  const arr = base64String.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  const extension = mimeTypes.extension(mime);
  return new File([u8arr], `${filename}.${extension}`, { type: mime });
}
