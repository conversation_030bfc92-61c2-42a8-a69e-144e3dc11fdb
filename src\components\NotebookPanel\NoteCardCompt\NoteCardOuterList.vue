<!-- 描述：相关笔记按列表的形式显示 每个笔记显示标题 -->
<template>
  <el-card
    ref="noteCardRef"
    :body-style="{ padding: '0px' }"
    class="box-card"
    shadow="hover"
  >
    <div v-if="localListMode === 'TYPE_LIST'">
      <van-cell
        :title="note.noteTitle"
        :label="note.ocrContent"
        @click="showAboutNote(note)"
      />
    </div>
    <div v-else>
      <div style="padding: 10px; background-color: #fdfaf3">
        <!-- card 模式 -->
        <div class="header">
          <el-text
            size="large"
            style="padding: 3px; color: black"
            :style="{ backgroundColor: note.color }"
          >
            {{ note.noteTitle }}
          </el-text>
        </div>
        <div>
          <label>句子原文：</label>
        </div>
        <div class="text-card">
          <div v-html="note.ocrContentSpan"></div>
        </div>
        <div v-if="note.keyword">
          <div>
            <label>标签：</label>
          </div>
          <div class="text-card">
            <el-text>{{ note.keyword }}</el-text>
          </div>
        </div>
        <div v-if="noteContentArray.length > 0">
          <div>
            <label>笔记：</label>
          </div>
          <div class="text-card">
            <div v-for="item in noteContentArray" :key="item.uid">
              <div v-if="item.type === 'TEXT'">
                <NoteCardText
                  :card-edit="false"
                  :item="item"
                  style="padding-left: 0"
                />
              </div>
              <div v-if="item.type === 'IMAGE'">
                <NoteCardImage :card-edit="false" :item="item" />
              </div>
              <div v-if="item.type === 'DRAWING'">
                <NoteFreeDrawing :edit-enabled="false" :item="item" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useElementSize } from "@vueuse/core";
import NoteCardText from "@/components/NotebookPanel/NoteCardCompt/NoteCardText.vue";
import NoteCardImage from "@/components/NotebookPanel/NoteCardCompt/NoteCardImage.vue";
import NoteFreeDrawing from "@/components/NotebookPanel/NoteCardCompt/NoteFreeDrawing.vue";

const props = defineProps({
  note: {
    type: Object,
    required: true,
  },
});

const noteCardRef = ref(null);
const { width } = useElementSize(noteCardRef);
const localListMode = ref("TYPE_CARD");

// 当宽度小于300px,切换为列表模式不显示card
watch(
  () => width.value,
  (newWidth) => {
    if (newWidth < 300) {
      localListMode.value = "TYPE_LIST";
    } else {
      localListMode.value = "TYPE_CARD";
    }
  }
);

const noteContentArray = computed(() => {
  return props.note.noteContentArray ?? [];
});

function showAboutNote() {
  //todo 需要在屏幕中间弹出弹窗 打开笔记对应的文章 显示笔记的上下文
  console.log("showAboutNote");
}
</script>
<style scoped>
.box-card {
  position: relative;
  padding: 0;
  border-radius: 8px;
  width: 100%;
  margin-bottom: 20px;
}

.header {
  margin-left: 30px;
  margin-right: 30px;
  padding-bottom: 5px;
  text-align: center;
}

.header h4 {
  color: black;
  text-align: center;
}

.text-card {
  /* background-color: white; */
  padding: 5px 0;
  /* margin: 10px; */
  border-radius: 5px;
}
</style>
