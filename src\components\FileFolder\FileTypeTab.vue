<template>
  <div style="margin-top: 10px">
    <van-row justify="center">
      <van-col span="5" style="padding-left: 15px" @click="SearchFile">
        <el-button :icon="Search" text />
      </van-col>
      <van-col span="14">
        <div style="text-align: center">
          <el-button-group>
            <el-button
              type="primary"
              round
              :plain="isTypeTabActive('date')"
              @click="changeSortType('date')"
            >
              日期
            </el-button>
            <el-button
              type="primary"
              round
              :plain="isTypeTabActive('name')"
              @click="changeSortType('name')"
            >
              名称
            </el-button>
          </el-button-group>
        </div>
      </van-col>
      <van-col span="5">
        <div
          v-if="cardType === 'strip'"
          @click="ChangeFileShowType"
          style="text-align: right; padding-right: 15px"
        >
          <el-button :icon="Menu" text />
        </div>
        <div
          v-else
          @click="ChangeFileShowType"
          style="text-align: right; padding-right: 15px"
        >
          <el-button :icon="List" text />
        </div>
      </van-col>
    </van-row>
  </div>
</template>

<script setup>
import { Search, Menu, List } from "@element-plus/icons-vue";
import { useFileFolderStore } from "@/stores/file-folder";
import { showAskFilenameDialog } from "@/components/FileFolder/Helper/AskFilenameDialog.jsx";

defineProps({
  cardType: {
    type: String,
  },
});

const emit = defineEmits(["onChangeFileShowType"]);

const fileFolderStore = useFileFolderStore();

const isTypeTabActive = (type) => {
  return fileFolderStore.sortType !== type;
};

const changeSortType = (type) => {
  fileFolderStore.sortType = type;
};

const SearchFile = async () => {
  const filename = await showAskFilenameDialog("文件", fileFolderStore.searchKeyword, { allowEmpty: true });
  fileFolderStore.searchKeyword = filename;
};

const ChangeFileShowType = () => {
  emit("onChangeFileShowType");
};
</script>

<style scoped></style>
