<template>
    <el-dialog :show-close="false" v-model="showEditDialog" title="标签管理" width="50%" :style="{ minWidth: '200px',paddingTop: '15px' }">
        <NoteCardTagsEditCard v-for="item in noteTotalTags" :key="item.value" :tag="item" @edit-finish="onEditFinish" @delete-tag="onDelete" />
        <NoteCardTagsNew ref="newTagComponent" @save-tag="addTag" />
        <div style="display: flex; justify-content: flex-end;padding-top: 20px;">
            <el-button @click="toggleDialog">完成</el-button>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { useTagsStore } from "@/stores/tags";

import NoteCardTagsNew from '@/components/EditFilterConditions/NoteCardTagsNew.vue'; // 编辑标签
import NoteCardTagsEditCard from '@/components/EditFilterConditions/NoteCardTagsEditCard.vue'; // 编辑标签

const tagsStore = useTagsStore();
const noteTotalTags = ref([]);
noteTotalTags.value = tagsStore.tags;
const newTagComponent = ref(null);

const showEditDialog = ref(false);
const toggleDialog = () => {
    showEditDialog.value = !showEditDialog.value;
    if (newTagComponent.value) {
        newTagComponent.value.finish();
    }
};

const onEditFinish = (updatedTag) => {
    let index = noteTotalTags.value.findIndex(item => item.value === updatedTag.value);
    if (index !== -1) {
        noteTotalTags.value[index].text = updatedTag.newText;
        noteTotalTags.value[index].value = updatedTag.newValue;
    }
};
const onDelete = (tag) => {
    let index = noteTotalTags.value.findIndex(item => item.value === tag.value);
    noteTotalTags.value.splice(index, 1);
    tagsStore.tags = noteTotalTags.value;
};
const addTag = (tag) => {
    noteTotalTags.value.push({
        text: tag,
        value: tag
    });
    tagsStore.tags = noteTotalTags.value;
};

defineExpose({
    toggleDialog
});

</script>
