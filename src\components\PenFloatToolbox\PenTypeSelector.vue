<template>
  <div>
    <!-- 画笔类型选择，基于element-plus -->
    <el-radio-group v-model="aliveToolValue" @change="changePenType">
      <el-radio-button label="PEN">
        <i class="bi bi-pen"></i>
      </el-radio-button>
      <el-radio-button label="HIGHLIGHT_PEN">
        <i class="bi bi-pen-fill"></i>
      </el-radio-button>
      <el-radio-button label="LINE">
        <i class="bi bi-dash"></i>
      </el-radio-button>
      <el-radio-button label="DOTTED_LINE">
        <i class="bi bi-dash"></i>
      </el-radio-button>
      <el-radio-button label="ERASER">
        <i class="bi bi-eraser"></i>
      </el-radio-button>
    </el-radio-group>
  </div>
</template>
<script setup>

import {ref} from "vue";

const props = defineProps({
  aliveTool: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["change-pen-type"]);

const aliveToolValue = ref(props.aliveTool);

function changePenType(value) {
  emit("change-pen-type", value);
}
</script>