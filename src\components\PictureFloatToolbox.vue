<template>
  <el-card
      v-if="aliveTool === 'ADD_IMAGE'"
      :body-style="{ padding: '2px' }"
      class="box-card"
  >
    <div class="card-button-view">
      <el-upload v-if="!inEditMode"
                 :auto-upload="false"
                 :on-change="addImageFile"
                 :show-file-list="false"
                 accept="image/*"
                 style="display: inline-flex"
      >
        <button class="el-button is-link"
                type="button"
        >
          <el-text style="color: black">添加图片</el-text>
        </button>
      </el-upload>
      <button v-if="!inEditMode"
              class="el-button is-link"
              type="button"
              @click="changeEditMode(true)"
      >
        <el-text style="color: black">编辑图片</el-text>
      </button>
      <button v-else
              class="el-button is-link"
              type="button"
              @click="changeEditMode(false)"
      >
        <el-text style="color: black">取消编辑</el-text>
      </button>
    </div>
  </el-card>
</template>

<script setup>
import {ref} from "vue";

defineProps({
  aliveTool: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["add-image-file", "change-edit-state"])

const inEditMode = ref(false);

function addImageFile(uploadFile) {
  emit("add-image-file", uploadFile.raw)
}

function changeEditMode(value) {
  inEditMode.value = value;
  emit("change-edit-state", value);
}
</script>

<style scoped>
.box-card {
  box-shadow: var(--el-box-shadow-dark);
  width: 200px;
  height: 55px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  position: absolute;
  bottom: 50px;
  left: 50%;
  margin-left: -100px;
}

.card-button-view {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: white;
}
</style>