<template>
  <div v-loading="loading">
    <div style="margin-bottom: 15px; display: flex; justify-content: end">
      <el-button type="primary" @click="reload">刷新</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="50" />
      <el-table-column
        prop="name"
        label="用户昵称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="createdAt"
        label="注册时间"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="isVIP" label="会员" />
      <el-table-column fixed="right" label="操作" min-width="120">
        <!-- <template #default>
          <el-button link type="primary" size="small">详情</el-button>
          <el-button link type="primary" size="small">编辑</el-button>
        </template> -->
      </el-table-column>
    </el-table>
    <div style="margin-top: 15px; display: flex; justify-content: center">
      <el-pagination
        v-model:current-page="currentPage"
        background
        layout="prev, pager, next"
        :page-count="totalPage"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { fetchUsers } from "@/components/AdminPanel/SubPanel/FetchAdminAPI";

type UserData = {
  name: string;
  vipStatus: string[];
  createdAt: string;
};

const loading = ref(true);
const totalPage = ref(1);
const currentPage = ref(1);
const tableDataRaw = ref<UserData[]>([]);
const tableData = computed(() => {
  return tableDataRaw.value.map((item) => {
    return {
      ...item,
      createdAt: new Date(item.createdAt).toLocaleString(),
      isVIP: item.vipStatus.length > 0 ? "是" : "否",
    };
  });
});

onMounted(() => {
  fetchData(1);
});

watch(currentPage, (newPage) => {
  fetchData(newPage);
});

function reload() {
  fetchData(currentPage.value);
}

function fetchData(page: number) {
  loading.value = true;
  fetchUsers(page)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        tableDataRaw.value = data.data?.users ?? [];
        totalPage.value = data.pagination?.totalPages ?? 1;
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
