<template>
  <el-main v-loading="loading">
    <VersionInfo />
    <div v-if="fetched">
      <el-row v-if="!hideVIPFeature" justify="center" style="margin: 10px 0 20px 0">
        <el-col :sm="16" :lg="12">
          <AvatarInfo />
        </el-col>
      </el-row>
      <el-row v-if="!hideVIPFeature" justify="center" style="margin: 10px 0 20px 0">
        <el-col :sm="16" :lg="12">
          <VIPBanner v-if="!userIsVIP" />
          <VIPInfo v-else />
        </el-col>
      </el-row>
      <el-row justify="center">
        <el-col :sm="16" :lg="12">
          <el-card shadow="never">
            <el-row>
              <el-col>
                <HelpFeedback />
                <el-divider style="margin: 12px 0" />
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <AboutApp />
                <el-divider style="margin: 12px 0" />
              </el-col>
            </el-row>
            <el-row v-if="!hideVIPFeature">
              <el-col>
                <CleanCache />
                <el-divider style="margin: 12px 0" />
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <SettingPage />
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div style="margin-bottom: 50px"></div>
    <BottomTabBar active="profile" />
  </el-main>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { useProfileStore } from "@/stores/profile";
import VersionInfo from "@/components/Profile/VersionInfo.vue";
import AvatarInfo from "@/components/Profile/AvatarInfo.vue";
import VIPBanner from "@/components/Profile/VIPCompt/VIPBanner.vue";
import VIPInfo from "@/components/Profile/VIPCompt/VIPInfo.vue";
import AboutApp from "@/components/Profile/ManageCompt/AboutAppPart.vue";
import HelpFeedback from "@/components/Profile/ManageCompt/HelpFeedbackPart.vue";
import CleanCache from "@/components/Profile/ManageCompt/CleanCachePart.vue";
import SettingPage from "@/components/Profile/ManageCompt/SettingPage.vue";
// @ts-expect-error 导入js组件
import BottomTabBar from "@/components/FileFolder/BottomTabBar.vue";
import { fetchUserProfile } from "@/components/Profile/Requests/FetchUserAPI";

const profileStore = useProfileStore();
const loading = ref(false);
const fetched = ref(false);
const userIsVIP = computed(() => profileStore.isVIP);
const hideVIPFeature = computed(() => profileStore.hideVIPFeature);

onMounted(() => {
  loading.value = true;
  fetchUserProfile()
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        profileStore.userProfile = data.data;
      } else {
        profileStore.userProfile = null;
      }
    })
    .catch(() => {})
    .finally(() => {
      fetched.value = true;
      loading.value = false;
    });
});
</script>
