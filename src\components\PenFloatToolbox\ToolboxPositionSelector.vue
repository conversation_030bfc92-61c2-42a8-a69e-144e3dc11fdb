<template>
  <div ref="target">
    <el-popover
        :visible="visible"
        :width="250"
        placement="bottom"
        title="工具栏位置"
    >
      <el-row>
        <el-col
            :span="6"
            @click="changeLocationToolsBox('topLeft-toolsbox-abslt')"
        >
          <div class="grid-content">
            <img
                alt=""
                src="@/assets/image/layout_left.png"
                style="width: 30px; height: 30px"
            />
            <div>
              <span class="span-btn-square">左侧</span>
            </div>
          </div>
        </el-col>
        <el-col
            :span="6"
            @click="changeLocationToolsBox('topRight-toolsbox-abslt')"
        >
          <div class="grid-content">
            <img
                alt=""
                src="@/assets/image/layout_right.png"
                style="width: 30px; height: 30px"
            />
            <div>
              <span class="span-btn-square">右侧</span>
            </div>
          </div>
        </el-col>
        <el-col
            :span="6"
            @click="changeLocationToolsBox('top-toolsbox-abslt')"
        >
          <div class="grid-content">
            <img
                alt=""
                src="@/assets/image/layout_top.png"
                style="width: 30px; height: 30px"
            />
            <div>
              <span class="span-btn-square">顶部</span>
            </div>
          </div>
        </el-col>
        <el-col
            :span="6"
            @click="changeLocationToolsBox('bottom-toolsbox-abslt')"
        >
          <div class="grid-content">
            <img
                alt=""
                src="@/assets/image/layout_bottom.png"
                style="width: 30px; height: 30px"
            />
            <div>
              <span class="span-btn-square">底部</span>
            </div>
          </div>
        </el-col>
      </el-row>
      <template #reference>
        <div class="btn-square-bottom" @click="toggleVisible">
          <img
              alt=""
              src="@/assets/image/color_setMore.png"
              class="w-100 h-100"
          />
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script setup>
import {ref, watch} from "vue";
import { onClickOutside } from '@vueuse/core'
import {useAppStore} from "@/stores/app";

const emit = defineEmits(["change-toolbox-location"]);

const appStore = useAppStore();
const visible = ref(false);
const target = ref(null)

onClickOutside(target, () => {
  visible.value = false;
})

watch(
    () => visible.value,
    () => appStore.showIframeCover = visible.value
)

function toggleVisible() {
  visible.value = !visible.value;
}

function changeLocationToolsBox(value) {
  visible.value = false;
  emit("change-toolbox-location", value);
}
</script>

<style scoped>
.el-row:last-child {
  margin-bottom: 0;
}

.el-col {
  border-radius: 4px;
}

.grid-content {
  height: 50px;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 20px;
}

.btn-square-bottom {
  width: 30px;
  height: 30px;
  border-radius: 7px;
  border: 0;
  padding: 0;
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.span-btn-square {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}
</style>