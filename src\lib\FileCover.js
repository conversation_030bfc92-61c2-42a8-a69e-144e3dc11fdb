import { getFileMeta, updateFileCover } from "@/lib/FileList.js";

export async function updateFileCoverData(fileMetaId, coverFile) {
  const formData = new FormData();
  formData.append("file", new File([coverFile], coverFile.name, { type: coverFile.type }));

  try {
    const response = await fetch("/api/opencv/compress", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();
    if (data.resCode && data.resCode === "SUCCESS") {
      const fileMeta = await getFileMeta(fileMetaId);
      if (data.data?.fileBase64 && fileMeta) {
        await updateFileCover(fileMeta.id, data.data.fileBase64);
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error("Cover update error:", error);
    return false;
  }
}

export async function asyncReadAsDataURL(file) {
  return new Promise((resolve) => {
    const fileReader = new FileReader();
    fileReader.onload = function () {
      return resolve({
        data: fileReader.result,
        name: file.name,
        size: file.size,
        type: file.type,
      });
    };
    fileReader.readAsDataURL(file);
  });
}
