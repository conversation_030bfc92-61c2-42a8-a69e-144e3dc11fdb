<template>
    <el-card v-if="!showInput" @click="onClickNew()" class="tag-card" shadow="never"
        :bodyStyle="{ padding: '5px 10px 5px 10px', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
        <el-button link type="primary">+ 新建标签</el-button>
    </el-card>
    <el-card v-if="showInput" class="tag-card no-border " shadow="never" :bodyStyle="{ padding: '5px 10px 5px 0px' }">
        <div class="card-flex">
            <div class="align-center-start tag-input">
                <el-input v-model="inputValue" @keyup.enter="onSave"></el-input>
            </div>
            <div class="align-center-end">
                <el-button link type="success" @click="onSave()"><el-icon><Finished /></el-icon>保存</el-button>
                <el-button link @click="onClose()"><el-icon><CloseBold /></el-icon>取消</el-button>
            </div>
        </div>
    </el-card>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';

const showInput = ref(false);
const inputValue = ref('');
const { emit } = getCurrentInstance();

const onClose = () => {
    showInput.value = false;
    inputValue.value = '';
};
const onSave = () => {
    showInput.value = false;
    if (!inputValue.value) {
        return;
    }
    emit('save-tag', inputValue.value);
    inputValue.value = '';
};
const onClickNew = () => {
    showInput.value = true;
};
const finish = () => {
    showInput.value = false;
};

defineExpose({
    finish
});
const emits = defineEmits(['save-tag']);


</script>
<style scoped>
.tag-card {
    margin-bottom: 8px;
}

.no-border {
    border: none !important;
}

.card-flex {
    display: flex;
    justify-content: space-between;
}

.align-center-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.align-center-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.tag-input {
    width: 100%;
    padding-right: 10px;
}
</style>
