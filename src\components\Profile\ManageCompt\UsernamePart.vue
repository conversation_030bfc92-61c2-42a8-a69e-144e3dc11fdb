<template>
  <div class="parent" @click="changeUsername">
    <el-text>用户昵称</el-text>
    <div class="content">
      <el-text type="info">{{ profileName }}</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useProfileStore } from "@/stores/profile";

const profileStore = useProfileStore();
const profileName = computed(() => profileStore.userProfile?.user?.name ?? "");

function changeUsername() {}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
