<template>
  <el-container style="height: 100vh">
    <el-header>
      <el-row style="padding: 20px 0">
        <el-col>
          <el-icon :size="22" @click="goBack">
            <ArrowLeft />
          </el-icon>
        </el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row justify="center" align="middle" style="height: 100%">
        <el-col :span="24" class="center-icon">
          <div @click="goTake">
            <el-icon :size="96">
              <Camera />
            </el-icon>
            <h1>继续拍照</h1>
          </div>
        </el-col>
      </el-row>
      <input
        ref="photoInputRef"
        type="file"
        accept="image/*"
        capture="environment"
        @change="goTakeChange"
        style="display: none"
      />
    </el-main>
    <el-footer height="200px">
      <el-row justify="space-between" align="middle">
        <el-col :span="12">
          <span>{{ photoList.length }} 张</span>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button link type="primary" @click="goCrop">
            <strong>完成</strong>
          </el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-scrollbar>
            <div class="scrollbar-flex-content">
              <p
                v-for="(item, index) in photoSrcList"
                :key="index"
                class="scrollbar-item"
              >
                <PhotoThumbnail
                  :index="index"
                  :src="item"
                  :preview-src-list="photoSrcList"
                  @delete="removeImage"
                />
              </p>
            </div>
          </el-scrollbar>
        </el-col>
      </el-row>
    </el-footer>
  </el-container>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useObjectUrl } from "@vueuse/core";
// @ts-expect-error import js store lib
import { useAppStore } from "@/stores/app.js";
import PhotoThumbnail from "@/components/PhotoTaker/PhotoThumbnail.vue";

const appStore = useAppStore();
const router = useRouter();
const photoList = ref<File[]>([]);
const photoSrcList = ref<(string | undefined)[]>();
const photoInputRef = ref<HTMLInputElement | null>(null);

onMounted(() => {
  photoList.value = appStore.currentFile ?? [];
  updateSrcList();
});

function goBack() {
  router.go(-1);
}

function goTake() {
  if (photoInputRef.value) {
    photoInputRef.value.value = "";
    photoInputRef.value.click();
  }
}

function goTakeChange(event: Event) {
  const files = (event.target as HTMLInputElement)?.files ?? [];
  if (files.length > 0) {
    const newFiles = [];
    for (let i = 0; i < files.length; i++) {
      newFiles.push(files[i]);
    }

    photoList.value = [...photoList.value, ...newFiles];
    updateSrcList();
  }
}

function updateSrcList() {
  photoSrcList.value = photoList.value.map((f) => useObjectUrl(f).value);
}

function removeImage(index: number) {
  photoList.value.splice(index, 1);
  updateSrcList();
}

function goCrop() {
  appStore.currentFile = photoList.value;
  if (photoList.value.length === 0) {
    goBack();
  } else {
    router.replace("/cropper-preview");
  }
}
</script>

<style scoped>
.center-icon {
  text-align: center;
  color: var(--el-color-primary);
}

.scrollbar-flex-content {
  display: flex;
}

.scrollbar-item {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: calc(200px - 24px - 20px);
  margin: 10px;
  border-radius: 4px;
}
</style>
