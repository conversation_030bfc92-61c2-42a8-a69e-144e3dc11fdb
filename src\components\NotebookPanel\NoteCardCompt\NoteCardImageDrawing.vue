<template>
  <div ref="divRef" style="width: 100%" :style="activeStyle">
    <div style="width: 100%; display: flex; justify-content: center">
      <v-stage
        ref="stage"
        :config="stageConfig"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @touchend="handleTouchEnd"
        @touchmove="handleTouchMove"
        @touchstart="handleTouchStart"
      >
        <v-layer>
          <v-image v-if="props.item.value" :config="{ image: imageObj }" />
        </v-layer>
        <v-layer>
          <v-line v-for="line in penLines" :key="line.uid" :config="line" />
        </v-layer>
      </v-stage>
    </div>
    <Teleport to="body">
      <PenFloatToolbox
        v-if="drawingEnabled"
        ref="penBoxRef"
        alive-tool="PEN"
        :alive-color="aliveColor"
        @change-pen-color="changeAliveColor"
        @change-pen-width="changeAliveLineWidth"
      />
    </Teleport>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useEventListener } from "vue-hooks-plus";
import { onClickOutside, useElementSize } from "@vueuse/core";
import PenFloatToolbox from "@/components/PenFloatToolbox/PenFloatToolbox.vue";

const props = defineProps({
  editEnabled: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    required: true,
  },
});

const emits = defineEmits(["update-item", "update-state"]);

const imageObj = ref(null);
const imageWidth = ref(0);
const imageHeight = ref(0);
onMounted(() => {
  const image = new window.Image();
  image.src = props.item.value;
  image.onload = function () {
    imageWidth.value = this.width ?? 0;
    imageHeight.value = this.height ?? 0;
    imageObj.value = image;
  };
});

const divRef = ref();
const penBoxRef = ref();
const divSize = useElementSize(divRef);

onClickOutside(
  divRef,
  () => {
    emits("update-state", false);
  },
  { ignore: [penBoxRef] }
);

const heightScale = computed(() => {
  return divSize?.height?.value && imageHeight.value
    ? divSize.height.value / imageHeight.value
    : 1;
});
const widthScale = computed(() => {
  return divSize?.width?.value && imageWidth.value
    ? divSize.width.value / imageWidth.value
    : 1;
});
const stageConfig = computed(() => {
  return {
    width: divSize?.width?.value ?? imageWidth.value,
    height: divSize?.height?.value ? divSize.height.value : imageHeight.value,
    scaleX: widthScale.value,
    scaleY: heightScale.value,
  };
});

const drawingEnabled = computed(() => {
  return props.editEnabled;
});

const activeStyle = computed(() => {
  return {
    border: drawingEnabled.value
      ? "1px solid #409eff"
      : "1px solid transparent",
  };
});

const penLines = ref(props.item.lines ?? []);
const isDrawing = ref(false);
const aliveColor = ref("rgb(0, 0, 0)"); //当前工具 使用中颜色
const aliveLineWidth = ref(10); //当前工具 使用中线宽高

watch(
  () => props.item.lines?.length,
  () => {
    penLines.value = props.item.lines ?? [];
  }
);

function handleMouseDown(e) {
  if (!drawingEnabled.value) {
    return;
  }
  isDrawing.value = true;
  const pos = e.target?.getStage()?.getPointerPosition();
  if (!pos) {
    return;
  }

  // 按比例缩放
  pos.x = pos.x / widthScale.value;
  pos.y = pos.y / heightScale.value;

  const newLine = {
    uid: Date.now(),
    stroke: aliveColor.value,
    strokeWidth: aliveLineWidth.value / 2,
    tension: 0.5,
    lineCap: "round",
    lineJoin: "round",
    points: [pos.x, pos.y],
  };
  penLines.value = [...penLines.value, newLine];
}

function handleMouseMove(e) {
  // no drawing - skipping
  if (!isDrawing.value || !drawingEnabled.value) {
    return;
  }

  const stage = e.target.getStage();
  const point = stage?.getPointerPosition();
  const lastLine = penLines.value[penLines.value.length - 1];
  if (!lastLine || !point) {
    return;
  }

  // 按比例缩放
  point.x = point.x / widthScale.value;
  point.y = point.y / heightScale.value;

  // add point
  lastLine.points = lastLine.points.concat([point.x, point.y]);

  // replace last
  penLines.value = penLines.value
    .slice(0, penLines.value.length - 1)
    .concat(lastLine);
}

function handleMouseUp() {
  isDrawing.value = false;
  if (drawingEnabled.value) {
    emits("update-item", { ...props.item, lines: penLines.value });
  }
}

function handleTouchStart(e) {
  e.evt.preventDefault();
  handleMouseDown(e);
}

function handleTouchMove(e) {
  e.evt.preventDefault();
  handleMouseMove(e);
}

function handleTouchEnd() {
  handleMouseUp();
}

function changeAliveColor(color) {
  aliveColor.value = color;
}

function changeAliveLineWidth(width) {
  aliveLineWidth.value = width;
}

useEventListener("mouseup", handleMouseUp, { target: window });
</script>

<style scoped></style>
