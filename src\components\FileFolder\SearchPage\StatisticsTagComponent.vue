<template>
    <div style="margin-left: 10px;margin-right: 10px;margin-top: 15px;">
        <el-text>标签2</el-text>
        <van-checkbox-group v-model="checked" shape="square" @change="handleChange">
            <van-checkbox v-for="(tag, index) in tagList" :key="index" :name="tag.name" style="margin: 8px;">
                <div style="display: flex;justify-content: space-between;">
                    <div>
                        {{ tag.name }}
                    </div>
                    <div>
                        <van-tag round  type="primary" style="margin-left: 10px;">10</van-tag>
                    </div>
                </div>
            </van-checkbox>
        </van-checkbox-group>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import tagList from '@/components/FileFolder/tagList.json';

const checked = ref([]);
const handleChange = (value) => {
    console.log(value);
    console.log(checked.value);
}

</script>
<style scoped></style>
 