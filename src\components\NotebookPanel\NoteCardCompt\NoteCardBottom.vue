<template>
  <el-dropdown trigger="click">
    <el-button text type="primary">
      更多<i class="bi bi-chevron-compact-down" style="margin-left: 5px;"></i>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <!-- <el-dropdown-item>
          <i class="bi bi-palette-fill"></i>更改颜色
        </el-dropdown-item>
        <el-dropdown-item>
          <i class="bi bi-card-checklist"></i>整理到卡片组
        </el-dropdown-item> -->
        <el-dropdown-item @click="deleteNote">
          <el-text type="danger">
            <i class="bi bi-trash3-fill"></i>删除
          </el-text>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
const emits = defineEmits(['delete-note']);

function deleteNote() {
  emits('delete-note');
}
</script>
