<template>
  <div class="parent" @click="showPolicy">
    <el-text>隐私政策</el-text>
    <div class="content">
      <el-text type="info">隐私政策</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
  <el-dialog
    v-model="showDialog"
    title="隐私政策"
    top="50px"
    width="90%"
    :append-to-body="true"
    :show-close="true"
  >
    <PrivacyPolicyText />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import PrivacyPolicyText from "@/components/Profile/DocumentCompt/PrivacyPolicyText.vue";

const showDialog = ref(false);
function showPolicy() {
  showDialog.value = true;
}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
