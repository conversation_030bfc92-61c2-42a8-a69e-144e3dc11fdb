import { ref } from "vue";
import { showDialog, closeDialog, Form, CellGroup, Field, Button } from "vant";

export const showAskFilenameDialog = async (type, filename, options) => {
  const { allowEmpty } = options ?? {};
  return new Promise((resolve, reject) => {
    const timestamp = Date.now().toString();
    const value = ref(filename);

    function onSubmit() {
      closeDialog();
      resolve(value.value);
    }

    function onCancel() {
      closeDialog();
      reject(value.value);
    }

    showDialog({
      title: type,
      message: () => (
        <Form key={timestamp} onSubmit={() => onSubmit()}>
          <CellGroup>
            <Field
              v-model={value.value}
              placeholder={`请输入${type}名称`}
              rules={[{ required: !allowEmpty, message: `请输入${type}名称` }]}
            />
          </CellGroup>
          <div
            style={{
              marginTop: "8px",
              display: "flex",
              justifyContent: "space-between",
              gap: "32px",
            }}
          >
            <Button block onClick={() => onCancel()}>
              取消
            </Button>
            <Button block type="primary" native-type="submit">
              确定
            </Button>
          </div>
        </Form>
      ),
      showConfirmButton: false,
      showCancelButton: false,
    });
  });
};
