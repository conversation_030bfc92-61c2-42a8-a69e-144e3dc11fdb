<template>
  <div ref="noteCardTextRef" class="text-container" :style="borderStyle">
    <div v-if="showInput && cardEdit" style="width: 100%;">
      <el-input ref="input"
                v-model="itemValue"
                :autosize="{ minRows: 2}"
                placeholder="请输入内容"
                style="width: 100%;"
                type="textarea"
                @blur="onChanged"
      />
    </div>
    <div v-else class="text-wrapper" >
      <StretchedLink v-if="cardEdit" @click="changeShowInput"/>
      <el-text v-if="item.value" tag="p" class="text">
        {{ item.value }}
      </el-text>
      <el-text v-else tag="p" type="info">
        添加内容...
      </el-text>
    </div>
  </div>
</template>

<script setup>
import {ref, nextTick, computed, onMounted} from 'vue';
import StretchedLink from "@/components/NotebookPanel/NoteCardCompt/StretchedLink.vue";

const emits = defineEmits(['update-item']);
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  cardEdit: {
    type: Boolean,
    required: true,
  },
});

const noteCardTextRef = ref(null);
const showInput = ref(false)
const input = ref(null);
const itemValue = ref(props.item.value);

const borderStyle = computed(() => {
  return {
    "border-left": props.item?.isContent ? "4px solid darkgray" : "4px solid transparent",
  }
})

onMounted(() => {
  if (props.cardEdit && props.item && !props.item.value) {
    nextTick(() => {
      scrollToComponent();
    })
  }
})

function scrollToComponent() {
  if (noteCardTextRef.value) {
    noteCardTextRef.value?.scrollIntoView({ behavior: 'smooth', block: 'end' });
  }
}

function changeShowInput() {
  showInput.value = true
  nextTick(() => {
    input.value?.focus()
  })
}

function onChanged() {
  showInput.value = false
  emits('update-item', {...props.item, value: itemValue.value});
}
</script>

<style scoped>
.text-container {
  display: flex;
  min-height: 32px;
  padding-left: 16px;
}

.text-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-wrapper .text {
  white-space: pre-wrap;
}
</style>