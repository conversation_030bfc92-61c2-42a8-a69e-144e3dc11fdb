<template>
  <div style="display: none"></div>
</template>
<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { ElLoading } from "element-plus";
import { UAParser } from "ua-parser-js";
// @ts-expect-error import js lib
import { useAppStore } from "@/stores/app";
import { useProfileStore } from "@/stores/profile";
import { fetchUserProfile } from "@/components/Profile/Requests/FetchUserAPI";

const router = useRouter();
const appStore = useAppStore();
const profileStore = useProfileStore();
const isAppleDevice = ref(false);

onMounted(() => {
  init();
});

onBeforeUnmount(() => {
  const timeout = isAppleDevice.value ? 500 : 200;
  setTimeout(() => {
    if (typeof window.ReactNativeWebView !== "undefined") {
      const data = {
        action: "app-stop-loading",
      };
      window.ReactNativeWebView?.postMessage(JSON.stringify(data));
    }
  }, timeout);
});

async function init() {
  const loading = ElLoading.service({ lock: true });

  // get app version
  const query = router.currentRoute.value.query;
  if (query.version) {
    appStore.appVersion = query.version as string;
  }

  // parse user-agent
  const parser = new UAParser();
  const result = parser.getResult();
  if (result.device.type === "mobile") {
    appStore.deviceType = "mobile";
  } else if (result.device.type === "tablet") {
    appStore.deviceType = "tablet";
  } else {
    appStore.deviceType = "web";
  }

  // 当前是apple设备时，隐藏会员功能
  isAppleDevice.value = result.device.vendor === "Apple";
  profileStore.hideVIPFeature = isAppleDevice.value;

  // 会员信息同步
  profileStore.syncFreeTrialCount();
  try {
    const data = await fetchUserProfile();
    if (data.resCode && data.resCode === "SUCCESS") {
      profileStore.userProfile = data.data;
    } else {
      profileStore.userProfile = null;
    }
  } catch {
    // ignore error
  }

  // get queries from url
  const { to, error, success } = router.currentRoute.value.query;
  if (to) {
    router.replace({ name: to as string, query: { error, success } });
  } else {
    router.replace({ name: "root" });
  }

  loading.close();
}
</script>
