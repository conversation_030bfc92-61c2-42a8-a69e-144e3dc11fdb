<template>
    <el-card v-if="!showInput" class="tag-card" shadow="never" :body-style="{ padding: '5px 10px 5px 10px' }">
        <div class="card-flex">
            <div class="align-center-start">
                #{{ tag.text }}
            </div>
            <div>
                <el-button link type="danger" @click="onDelete(tag)"><el-icon>
                        <Delete />
                    </el-icon>删除</el-button>
                <el-button link type="primary" @click="onEdit(tag)"><el-icon>
                        <Edit />
                    </el-icon>编辑</el-button>
            </div>
        </div>
    </el-card>
    <el-card v-if="showInput" class="tag-card no-border" shadow="never" :bodyStyle="{ padding: '5px 10px 5px 0px' }">
        <div class="card-flex">
            <div class="align-center-start tag-input">
                <el-input style="width: 100%" v-model="inputValue" @keyup.enter="onEditFinish()"></el-input>
            </div>
            <div class="align-center-end">
                <el-button link type="success" @click="onEditFinish()"><el-icon><Finished /></el-icon>保存</el-button>
                <el-button link @click="onCancel()"><el-icon><CloseBold /></el-icon>取消</el-button>
            </div>
        </div>
    </el-card>
</template>

<script setup>
import { ref, getCurrentInstance,onMounted } from 'vue';

const props = defineProps({
    tag: {
        type: Object,
        required: true
    }
});

onMounted(() => {
    inputValue.value = props.tag.text;
});

const showInput = ref(false);
const inputValue = ref('');
const onDelete = (tag) => {
    emit('delete-tag', tag);
};
const onEdit = (tag) => {
    showInput.value = true;
    inputValue.value = tag.text;
};

const { emit } = getCurrentInstance();

const onEditFinish = () => {
    showInput.value = false;
    if (!inputValue.value) {
        return;
    }
    const updatedTag = {
        ...props.tag,
        newText: inputValue.value,
        newValue: inputValue.value
    };
    emit('edit-finish', updatedTag);
    inputValue.value = '';
};
const finish = () => {
    showInput.value = false;
};
const onCancel = () => {
    showInput.value = false;
    inputValue.value = '';
};

defineExpose({
    finish
});
const emits = defineEmits(['delete-tag','edit-finish']);


</script>
<style scoped>
.tag-card {
    margin-bottom: 8px;
}

.no-border {
    border: none !important;
}

.card-flex {
    display: flex;
    justify-content: space-between;
}

.align-center-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.align-center-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.tag-input {
    width: 100%;
    padding-right: 10px;
}
</style>
