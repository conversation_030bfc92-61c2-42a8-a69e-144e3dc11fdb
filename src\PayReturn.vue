<template>
  <el-empty v-if="showType === 'error'" description="支付失败">
    <el-alert
      type="error"
      title="支付失败"
      description="支付失败，请重新支付"
      show-icon
      :closable="false"
    />
  </el-empty>
  <el-empty v-if="showType === 'success'" description="支付成功">
    <el-alert
      type="success"
      title="支付成功"
      description="支付成功，请返回APP查看"
      show-icon
      :closable="false"
    />
  </el-empty>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const showType = ref("");

onMounted(() => {
  const { error, success } = router.currentRoute.value.query;
  if (error) {
    showType.value = "error";
  } else if (success) {
    showType.value = "success";
  }
});
</script>
