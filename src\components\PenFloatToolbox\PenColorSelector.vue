<template>
  <div class="btn-wrapper">
    <div v-for="(colorItem, index) in panelColors" :key="colorItem">
      <el-button
          v-if="colorItem !== aliveColor"
          :style="{ background: colorItem }"
          class="btn-square-bottom"
          @click="changeColorIndex(colorItem)"
      ></el-button>
      <div v-else @click="changeColorIndex(colorItem)">
        <el-popover
            :width="250"
            placement="bottom"
            title="笔颜色"
            trigger="click"
        >
          <!-- 从默认颜色中选择 -->
          <div style="display: flex; flex-wrap: wrap">
            <div
                v-for="colorStr in defaultColors"
                :key="colorStr"
                class="color-button-circle"
            >
              <div
                  v-if="colorStr !== aliveColor"
                  :style="{ color: colorStr }"
                  @click="editDefaultColors(index, colorStr)"
              >
                <i class="bi bi-circle-fill"></i>
              </div>
              <div v-else :style="{ color: colorStr }">
                <i class="bi bi-check-circle-fill"></i>
              </div>
            </div>
          </div>
          <template #reference>
            <div
                :style="{ borderColor: colorItem }"
                class="btn-square-bottom-borderColor"
            >
              <div
                  :style="{ background: colorItem }"
                  class="btn-square-bottom-borderColor-inner"
              ></div>
            </div>
          </template>
        </el-popover>
      </div>
    </div>
  </div>
</template>
<script setup>
import {onMounted} from "vue";
import {DefaultColors} from "@/components/PenFloatToolbox/DefaultValues";
import {usePenStore} from "@/stores/pen";

const props = defineProps({
  aliveTool: {
    type: String,
    default: "",
  },
  aliveColor: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["change-pen-color"]);

const defaultColors = [...DefaultColors.NORMAL];
const penStore = usePenStore();
const currentPen = penStore.getPen(props.aliveTool);
// 面板显示颜色 切换不同工具时，颜色会变化。能修改 存储到原生APP文件中
const panelColors = currentPen?.panelColors;

onMounted(() => {
  if (currentPen?.color) {
    emit("change-pen-color", currentPen.color);
  }
});

function changeColorIndex(colorItem) {
  currentPen.color = colorItem;
  emit("change-pen-color", colorItem);
}

function editDefaultColors(index, colorItem) {
  currentPen.color = colorItem;
  panelColors[index] = colorItem;
  emit("change-pen-color", colorItem);
}
</script>

<style scoped>
.btn-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.btn-square-bottom {
  width: 30px;
  height: 30px;
  border-radius: 7px;
  border: 0;
  padding: 0;
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-square-bottom-borderColor {
  width: 30px;
  height: 30px;
  border-radius: 7px;
  margin: 5px;
  border: 3px solid;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-square-bottom-borderColor-inner {
  width: 20px;
  height: 20px;
  border-radius: 5px;
}

.color-button-circle {
  padding: 10px;
  font-size: 1.5rem;
}
</style>