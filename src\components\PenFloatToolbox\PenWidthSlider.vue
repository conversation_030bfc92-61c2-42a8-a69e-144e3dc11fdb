<template>
  <el-popover :width="250" placement="bottom" title="笔粗细" trigger="click">
    <div class="slider-demo-block">
      <span class="demonstration">{{ sliderValue / divider }}</span>
      <el-slider
        v-model="sliderValue"
        :min="10"
        :format-tooltip="formatTooltip"
        @change="changePenWidth"
      />
    </div>
    <template #reference>
      <div
        :class="activeBtnClass"
        class="btn-square-bottom"
        @click="changeAliveLineType"
      >
        <div
          :style="{ height: sliderValue / 8 + 'px' }"
          class="btn-line-demo"
        ></div>
      </div>
    </template>
  </el-popover>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { usePenStore } from "@/stores/pen";

const props = defineProps({
  aliveTool: {
    type: String,
    default: "",
  },
  lineWidthIndex: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["change-line-width"]);
const penStore = usePenStore();
const currentPen = penStore.getPen(props.aliveTool);
const sliderValue = ref(currentPen?.lineWidths[props.lineWidthIndex] ?? 0);

const divider = computed(() => {
  return props.aliveTool === "ERASER" ? 10 : 50;
});

const activeBtnClass = computed(() => {
  return {
    "btn-square-bottom-line-alive":
      props.lineWidthIndex === currentPen?.activeWidthIndex,
  };
});

onMounted(() => {
  if (props.lineWidthIndex === currentPen?.activeWidthIndex) {
    emit("change-line-width", sliderValue.value / 4);
  }
});

function formatTooltip(val) {
  return val / divider.value;
}

function changePenWidth(val) {
  currentPen.lineWidths[props.lineWidthIndex] = val;
  emit("change-line-width", val / 4);
}

function changeAliveLineType() {
  currentPen.activeWidthIndex = props.lineWidthIndex;
  emit("change-line-width", sliderValue.value / 4);
}
</script>

<style scoped>
.slider-demo-block {
  display: flex;
  align-items: center;
}

.slider-demo-block .el-slider {
  margin-top: 0;
  margin-left: 12px;
}

.slider-demo-block .demonstration {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 44px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0;
}

.slider-demo-block .demonstration + .el-slider {
  flex: 0 0 70%;
}

.btn-square-bottom {
  width: 30px;
  height: 30px;
  border-radius: 7px;
  border: 0;
  padding: 0;
  margin: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-line-demo {
  width: 20px;
  background-color: rgb(0, 0, 0);
  border-radius: 30%;
}

.btn-square-bottom-line-alive {
  background-color: rgb(144, 147, 153);
}
</style>
