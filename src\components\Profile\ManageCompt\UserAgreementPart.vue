<template>
  <div class="parent" @click="showAgreement">
    <el-text>用户协议</el-text>
    <div class="content">
      <el-text type="info">用户协议</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
  <el-dialog
    v-model="showDialog"
    title="用户协议"
    top="50px"
    width="90%"
    :append-to-body="true"
    :show-close="true"
  >
    <UserAgreementText />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import UserAgreementText from "@/components/Profile/DocumentCompt/UserAgreementText.vue";

const showDialog = ref(false);
function showAgreement() {
  showDialog.value = true;
}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
