<template>
  <el-space :size="15" alignment="start" style="margin: 5px;">
    <el-text v-for="item in noteTags" :key="item.value"
             class="mr-2"
             type="success"
    >
      #{{ item.text }}
    </el-text>
    <el-popover v-if="cardEdit"
                :visible="visible"
                :width="250"
                placement="top"
                title="选择标签"
                trigger="click"
    >
      <el-select v-model="selectedOption"
                 collapse-tags
                 multiple
                 placeholder="请选择标签"
                 @change="onChanged"
      >
        <el-option v-for="item in noteTotalTags" :key="item.value"
                   :label="item.text"
                   :value="item.value"
        />
      </el-select>
      <template #reference>
        <el-text class="mr-2" type="primary"  @click="visible = true">
          <el-icon style="margin-right: 3px;">
            <Edit />
          </el-icon>
          选择标签
        </el-text>
      </template>
      <el-button text size="small" type="primary" class="edit-tags-button" @click="onAddOption">
        管理标签
      </el-button>
    </el-popover>
  </el-space>
  <NoteCardTagsEdit ref="noteCardTagsEditRef"/>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { Edit } from "@element-plus/icons-vue";
import { useTagsStore } from "@/stores/tags";
import NoteCardTagsEdit from '@/components/EditFilterConditions/NoteCardTagsEdit.vue'; // 编辑标签

const emits = defineEmits(['update-tags']);
const props = defineProps({
  noteTags: {
    type: Array,
    required: true
  },
  cardEdit: {
    type: Boolean,
    required: true
  }
});

const selectedOption = ref([]);
const noteTags = ref(props.noteTags);
const tagsStore = useTagsStore();
const noteTotalTags = tagsStore.tags;
const visible = ref(false);
const noteCardTagsEditRef = ref(null);

onMounted(() => {
  selectedOption.value = props.noteTags.map(item => item.value);
});

function onChanged() {
  noteTags.value = noteTotalTags.filter(item => selectedOption.value.includes(item.value));
  emits('update-tags', noteTags.value);
}

function onAddOption() {
  // 管理标签
  visible.value = false; //隐藏el-popover
  noteCardTagsEditRef.value?.toggleDialog();
}
</script>
<style scoped>
.edit-tags-button {
  margin-top: 10px;
}
</style>