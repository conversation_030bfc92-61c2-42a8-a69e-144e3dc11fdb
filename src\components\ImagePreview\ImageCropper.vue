<template>
  <el-container>
    <el-main>
      <div class="fullscreen">
        <cropper
          ref="cropperRef"
          class="cropper"
          :src="file"
          :canvas="{
            minHeight: 0,
            minWidth: 0,
            maxHeight: 4096,
            maxWidth: 4096,
          }"
          image-restriction="fit-area"
        ></cropper>
      </div>
    </el-main>
    <el-aside width="80px">
      <div class="aside-container">
        <div class="btn-part-container">
          <el-button
            type="primary"
            class="aside-button"
            @click="rotateClockwise"
          >
            <i class="bi bi-arrow-clockwise"></i>
          </el-button>
          <el-button
            type="primary"
            class="aside-button"
            style="margin-left: 0"
            @click="rotateCounterClockwise"
          >
            <i class="bi bi-arrow-counterclockwise"></i>
          </el-button>
        </div>
        <div class="btn-part-container">
          <el-button type="success" class="aside-button" @click="cropImage">
            <i class="bi bi-check"></i>
          </el-button>
          <el-button
            type="danger"
            class="aside-button"
            style="margin-left: 0"
            @click="backToIndex"
          >
            <i class="bi bi-x"></i>
          </el-button>
        </div>
        <div class="btn-part-container"></div>
      </div>
    </el-aside>
  </el-container>
</template>

<script lang="ts" setup>
import { useTemplateRef } from "vue";
import { useObjectUrl } from "@vueuse/core";
import { ElLoading, ElNotification } from "element-plus";

import { Cropper } from "vue-advanced-cropper";
import "vue-advanced-cropper/dist/style.css";

const props = defineProps({
  editFile: {
    type: File,
    default: () => undefined,
  },
});

const emits = defineEmits(["edit-ok", "edit-cancel"]);

const cropperIns = useTemplateRef("cropperRef");

const file = useObjectUrl(props.editFile);

function backToIndex() {
  emits("edit-cancel");
}

function rotateClockwise() {
  cropperIns.value?.rotate(45);
}

function rotateCounterClockwise() {
  cropperIns.value?.rotate(-45);
}

function cropImage() {
  const loading = ElLoading.service({ lock: true });
  const result = cropperIns.value?.getResult();
  const canvas = result?.canvas;
  if (!canvas) {
    ElNotification({
      title: "错误",
      message: "裁剪失败",
      type: "error",
    });
    loading.close();
    return;
  }

  canvas.toBlob((blob) => {
    if (!blob) {
      ElNotification({
        title: "错误",
        message: "裁剪失败",
        type: "error",
      });
      loading.close();
      return;
    }

    const file = new File(
      [blob],
      props.editFile?.name || `image_${Date.now()}.png`,
      {
        type: "image/png",
      }
    );
    emits("edit-ok", file);

    loading.close();
  }, "image/png");
}
</script>

<style lang="scss" scoped>
.fullscreen {
  width: 100%;
  height: calc(100vh - var(--el-main-padding) * 2);

  @media screen and (orientation: portrait) {
    height: 100%;
  }

  .cropper {
    width: 100%;
    height: 100%;
  }
}

.aside-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 20px 8px;

  @media screen and (orientation: portrait) {
    display: grid;
    grid-auto-columns: minmax(0, 1fr);
    grid-auto-flow: column;
    gap: 10px;

    > .btn-part-container {
      display: grid;
      grid-auto-rows: minmax(0, 1fr);
      grid-auto-flow: column;
      gap: 10px;

      &:last-child {
        display: none;
      }
    }
  }

  .aside-button {
    width: 100%;

    &:not(:last-child) {
      margin-bottom: 15px;

      @media screen and (orientation: portrait) {
        margin-bottom: 10px;
      }
    }
  }
}

@media screen and (orientation: portrait) {
  .el-aside {
    width: 100%;
  }

  .el-container {
    flex-direction: column;
    height: 100vh;
  }
}
</style>
