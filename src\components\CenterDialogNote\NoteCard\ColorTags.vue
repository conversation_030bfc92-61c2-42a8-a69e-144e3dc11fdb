<template>
  <el-scrollbar>
    <div class="scrollbar-flex-content">
      <div
        v-for="colorTag in ColorTags"
        :key="colorTag.id"
        :style="{ color: colorTag.color }"
        class="scrollbar-item"
        @click="sendChangeColorMessage(colorTag)"
      >
        <i :class="colorButtonClass(colorTag.color)"></i>
        <el-text :style="{ color: colorTag.color }">
          {{ colorTag.name }}
        </el-text>
      </div>
      <div class="scrollbar-item" @click="editColorTags()">
        <i class="bi bi-plus-circle" style="color: rgb(64, 158, 255)"></i>
        <el-text type="primary">自定义</el-text>
      </div>
    </div>
  </el-scrollbar>
  <NoteColorTagsEdit ref="colorTagsEditRef" @edit-finish="updateColorTags" />
</template>
<script setup>
import { onMounted, ref } from "vue";
import { listColorTags } from "@/lib/RectDatabase";
import { ColorTags as DefaultColorTags } from "@/components/CenterDialogNote/NoteCard/DefaultValues";
import NoteColorTagsEdit from "@/components/CenterDialogNote/NoteCard/NoteColorTagsEdit.vue";

const props = defineProps({
  color: {
    type: String,
    default: undefined,
  },
});

const ColorTags = ref([]);

onMounted(() => {
  loadColorTags();
});

async function loadColorTags() {
  const list = await listColorTags();
  if (list.length === 0) {
    ColorTags.value = DefaultColorTags;
  } else {
    ColorTags.value = list;
  }
}

// const rectsStore = useRectsStore();
const emit = defineEmits(["color-change"]);

function colorButtonClass(color) {
  return {
    "bi bi-circle-fill": color !== props.color,
    "bi bi-record-circle": color === props.color,
  };
}

function sendChangeColorMessage(colorTag) {
  // 传回颜色标签的颜色值
  emit("color-change", colorTag);
}

const colorTagsEditRef = ref(null);
function editColorTags() {
  // 编辑颜色标签数组，并将自定义数组保存到APP本地
  colorTagsEditRef.value?.toggleDialog();
}

function updateColorTags(tags) {
  ColorTags.value = tags;
}
</script>

<style scoped>
.box-card {
  padding: 0;
  width: 400px;
}

.scrollbar-flex-content {
  display: flex;
}

.scrollbar-item {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 5px;
  margin: 0 5px;
  text-align: center;
  border-radius: 5px;
}
.card-button {
  padding-left: 10px;
  padding-right: 10px;
  font-size: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
</style>
