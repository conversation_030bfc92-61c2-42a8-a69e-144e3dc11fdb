import { createFileListDbInstance } from "@/lib/AppDatabase";

// 获取数据库实例的辅助函数
function getDb() {
  return createFileListDbInstance();
}

export function createNewFileMeta(timestamp, parentId, type, name, notesCount) {
  const filename = extractFileName(name);
  const extension = extractFileExtension(name);
  return {
    id: timestamp,
    parentId: parentId ? parentId : "",
    type: type,
    name: type === "folder" ? name : filename,
    extension: extension,
    lastModified: timestamp,
    notesCount: notesCount,
    isNew: true,
    fileUidList: [],
  };
}

function extractFileName(filename) {
  const lastDotIndex = filename.lastIndexOf(".");
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    return filename;
  }

  return filename.substring(0, lastDotIndex);
}

function extractFileExtension(filename) {
  const extension = filename.split(".").pop().toLowerCase();
  return extension === "pdf" ? "pdf" : "jpg";
}

export async function getFileMetaList() {
  const db = getDb();
  return await db.files.toArray();
}

export async function getFolderMetaList(parentId) {
  const db = getDb();
  const list = await db.files
    .where("parentId")
    .equals(parentId)
    .and((fileMeta) => fileMeta.type === "folder")
    .toArray();
  list.sort((a, b) => {
    return Number(b.id) - Number(a.id);
  });
  return list;
}

export async function getFileMeta(metaId) {
  const db = getDb();
  return db.files.get(Number(metaId));
}

export async function putFileMeta(newFileMeta) {
  const db = getDb();
  return db.files.put(newFileMeta);
}

export async function deleteFileMeta(fileMeta) {
  const db = getDb();
  if (fileMeta.type === "folder") {
    // 当文件是文件夹时，删除其下的所有子文件，同时获取所有被删除的子文件的id
    const idList = [];
    const childFileList = await db.files
      .where("parentId")
      .equals(fileMeta.id)
      .toArray();
    for (const child of childFileList) {
      const childId = await deleteFileMeta(child);
      idList.push(childId);
    }
    await db.files.delete(fileMeta.id);
    await db.covers.delete(fileMeta.id);
    return idList;
  } else if (fileMeta.type === "file") {
    await db.files.delete(fileMeta.id);
    await db.covers.delete(fileMeta.id);
    return [fileMeta.id];
  }

  return [];
}

export async function updateParentFolderFileCount(fileMeta, diff) {
  if (!fileMeta.parentId) {
    return;
  }

  const db = getDb();
  const parentFileMeta = await getFileMeta(fileMeta.parentId);
  if (!parentFileMeta || parentFileMeta.type !== "folder") {
    return;
  }

  parentFileMeta.notesCount += diff;
  if (parentFileMeta.notesCount < 0) {
    parentFileMeta.notesCount = 0;
  }
  return db.files.put(parentFileMeta);
}

export async function updateFileCover(id, cover) {
  const db = getDb();
  return await db.covers.put({ id, cover });
}

export async function readFileCover(id) {
  const db = getDb();
  return await db.covers.get(id);
}
