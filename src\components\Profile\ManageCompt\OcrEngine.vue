<template>
  <div class="parent">
    <el-text>OCR引擎</el-text>
    <div class="content">
      <el-select
        v-model="selectedEngine"
        @change="handleChange"
        size="small"
        class="ocr-engine-select"
      >
        <el-option label="稳定" value="JS" />
        <el-option label="高速 Beta" value="WASM" />
      </el-select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElSelect, ElOption } from "element-plus";
import { getOcrEngineType, setOcrEngineType } from "@/lib/ConfigDatabase.ts";

const selectedEngine = ref<"JS" | "WASM">("JS");

onMounted(() => readOcrEngineType());

async function readOcrEngineType() {
  const ocrEngineType = await getOcrEngineType();
  selectedEngine.value = ocrEngineType;
}

// Handle selection change with validation
const handleChange = (value: string) => {
  if (value !== "JS" && value !== "WASM") {
    selectedEngine.value = "JS";
    return;
  }

  // Update the store
  setOcrEngineType(value).then(() => readOcrEngineType());
};
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
  min-width: 130px;
}

div.content > * {
  margin-left: 5px;
}
</style>
