<template>
  <div class="header">
    <div v-if="!isEditingTitle" class="header-outer">
      <div
        :style="{ backgroundColor: props.color }"
        style="padding-left: 5px; padding-right: 5px"
      >
        <h2 style="margin-right: 10px" @click="editTitle">
          {{ title }}
        </h2>
      </div>
    </div>
    <div v-else class="header-outer">
      <el-input v-model="title" @blur="finishEditing"></el-input>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: "white",
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const title = ref(props.title);
const isEditingTitle = ref(false);

watch(
  () => props.title,
  () => (title.value = props.title)
);

const editTitle = () => {
  if (props.readonly) {
    return;
  }
  isEditingTitle.value = true;
};

const finishEditing = () => {
  isEditingTitle.value = false;
  // emit('update:title', title.value);
};
</script>

<style scoped>
.header {
  padding-bottom: 5px;
}

.header h2 {
  color: black;
  text-align: center;
}

.header-outer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
