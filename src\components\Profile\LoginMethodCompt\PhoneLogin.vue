<template>
  <div class="parent">
    <el-text>手机号码</el-text>
    <div class="content">
      <el-text type="info">{{ account?.placeholder }}</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  account: {
    type: Object,
    default: () => null,
  },
});
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
