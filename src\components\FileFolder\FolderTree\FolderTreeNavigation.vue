<template>
  <ElText v-if="isRoot" class="mx-1" type="info">全部文件</ElText>
  <div v-else>
    <el-breadcrumb :separator-icon="ArrowRight" style="display: flex; justify-content: left; align-items: center; flex-wrap: wrap">
      <div>
        <ElText class="mx-1" type="primary" @click="goBackOneLevel">返回上一级</ElText> |
      </div>
      <el-breadcrumb-item>
        <ElText type="primary" truncated @click="goRoot">全部文件</ElText>
      </el-breadcrumb-item>
      <el-breadcrumb-item v-if="nodeArray.length > 1">
        <ElText type="info" truncated>...</ElText>
      </el-breadcrumb-item>
      <el-breadcrumb-item>
        <ElText type="primary" truncated class="breadcrumb-text">{{ nodeArray[nodeArray.length - 1].name }}</ElText>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
import {computed} from "vue";
import {ElText, ElBreadcrumb, ElBreadcrumbItem} from "element-plus";
import {ArrowRight} from "@element-plus/icons-vue";

const props = defineProps({
  nodeArray: {
    type: Array,
  }
})

const emits = defineEmits(['go-back-one-level', 'go-root'])

const isRoot = computed(() => props.nodeArray.length === 0)

function goBackOneLevel() {
  emits('go-back-one-level')
}

function goRoot() {
  emits('go-root')
}
</script>

<style scoped>
.breadcrumb-text {
  max-width: 8em;
}
</style>