import Dexie from "dexie";

// 文件列表数据库单例类
class FileListDatabase {
  constructor() {
    if (FileListDatabase.instance) {
      return FileListDatabase.instance;
    }

    this.db = null;
    FileListDatabase.instance = this;
  }

  getInstance() {
    // 如果已有实例且可用，直接返回
    if (this.db) {
      try {
        if (this.db.isOpen() && this.db.name === "fileList") {
          return this.db;
        }
      } catch (error) {
        console.warn("Existing fileListDb instance is not usable:", error);
        this.db = null;
      }
    }

    // 创建新实例
    this.db = new Dexie("fileList");
    this.db.version(1).stores({
      files: "&id, parentId, name, type",
      covers: "&id",
    });

    return this.db;
  }

  // 重置实例（用于测试或错误恢复）
  reset() {
    if (this.db) {
      try {
        this.db.close();
      } catch (error) {
        console.warn("Error closing fileList database:", error);
      }
    }
    this.db = null;
  }
}

// 导出单例实例获取函数
export const createFileListDbInstance = () => {
  const fileListDb = new FileListDatabase();
  return fileListDb.getInstance();
};

// 应用数据库单例类
class AppDatabase {
  constructor() {
    if (AppDatabase.instance) {
      return AppDatabase.instance;
    }

    this.db = null;
    AppDatabase.instance = this;
  }

  getInstance() {
    // 如果已有实例且可用，直接返回
    if (this.db) {
      try {
        if (this.db.isOpen() && this.db.name === "AppDatabase") {
          return this.db;
        }
      } catch (error) {
        console.warn("Existing appDb instance is not usable:", error);
        this.db = null;
      }
    }

    // 创建新实例
    this.db = new Dexie("AppDatabase");
    this.db.version(2).stores({
      colorTagData: "&id",
      penData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
      rectData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
      noteData: "&uid, fileId, pageIndex, [fileId+pageIndex]",
      ocrData: "&uid",
      configData: "&key",
    });

    return this.db;
  }

  // 重置实例（用于测试或错误恢复）
  reset() {
    if (this.db) {
      try {
        this.db.close();
      } catch (error) {
        console.warn("Error closing app database:", error);
      }
    }
    this.db = null;
  }
}

// 导出单例实例获取函数
export const createAppDbInstance = () => {
  const appDb = new AppDatabase();
  return appDb.getInstance();
};
