<template>
  <div>
    <el-button link @click="closeDialog">
      <el-icon :size="32">
        <ArrowLeft />
      </el-icon>
    </el-button>
    <el-card class="code-card" shadow="never">
      <el-text>
        <h3>请输入验证码</h3>
      </el-text>
      <el-text type="info"> 已发送验证码到 {{ maskedPhone }} </el-text>
      <div style="margin-top: 30px">
        <el-input
          v-model="code"
          placeholder="请输入验证码"
          clearable
          size="large"
        />
        <el-button
          :disabled="loading || !code"
          :loading="loading"
          type="primary"
          size="large"
          style="width: 100%; margin-top: 10px"
          @click="doCodeAction"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
      <div style="margin-top: 10px">
        <el-text v-if="countdown > 0" type="info">
          {{ countdown }} 秒后可重新发送
        </el-text>
        <el-button v-else link type="warning" @click="sendCodeAgain">
          重新发送验证码
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeUnmount, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { fetchSendSmsCode } from "@/components/Profile/Requests/FetchUserAPI";

const props = defineProps({
  phone: {
    type: String,
    default: "",
  },
  preCountDown: {
    type: Number,
    default: 0,
  },
  confirmButtonText: {
    type: String,
    default: "登录",
  },
  parentLoading: {
    type: Boolean,
    default: false,
  },
  interceptCodeResend: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["close-dialog", "do-code-action", "do-code-resend"]);

onMounted(() => {
  startCountdown(props.preCountDown);
});

const maskedPhone = computed(() => {
  const phone = props.phone;
  if (phone.includes("*")) {
    return phone;
  }

  if (!phone || phone.length <= 7) {
    return "";
  }

  return phone.slice(0, 3) + "*".repeat(phone.length - 7) + phone.slice(-4);
});

const code = ref("");
const comptLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref();

const loading = computed(() => {
  return props.parentLoading || comptLoading.value;
});

function closeDialog() {
  emits("close-dialog");
}

function startCountdown(preCountDown?: number) {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }

  countdown.value = preCountDown && preCountDown > 0 ? preCountDown : 60;

  countdownTimer.value = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value);
    }
  }, 1000);
}

function doCodeAction() {
  if (!checkCodeIsValid()) {
    ElMessage.error("验证码错误，请重新输入");
    return;
  }

  emits("do-code-action", props.phone, code.value);
}

function checkCodeIsValid() {
  // 检查验证码是否为6位数字
  return /^\d{6}$/.test(code.value);
}

function sendCodeAgain() {
  if (props.interceptCodeResend) {
    return emits("do-code-resend");
  }

  comptLoading.value = true;
  fetchSendSmsCode(props.phone)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        ElMessage.success("验证码已发送，请注意查收");
        startCountdown();
      } else if (data.resCode && data.resCode === "HAS_SEND_CODE") {
        ElMessage.error(data.message);
        startCountdown(data.data?.countDown);
      } else if (data.message) {
        ElMessage.error(data.message);
      } else {
        ElMessage.error("发送验证码失败，请稍后重试");
      }
    })
    .catch(() => {
      ElMessage.error("发送验证码失败，请稍后重试");
    })
    .finally(() => {
      comptLoading.value = false;
    });
}

onBeforeUnmount(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});
</script>

<style lang="scss" scoped>
.code-card {
  max-width: 400px;
  margin: 30px auto;
}
</style>
