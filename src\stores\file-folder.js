import { defineStore } from "pinia";
import { ref } from "vue";

export const useFileFolderStore = defineStore("file-folder", () => {
  const fileList = ref([]);
  const fileCoverList = ref([]);
  const firstFileListQueried = ref(false);
  const sortType = ref("date");
  const searchKeyword = ref("");

  return {
    fileList,
    fileCoverList,
    firstFileListQueried,
    sortType,
    searchKeyword,
  };
});
