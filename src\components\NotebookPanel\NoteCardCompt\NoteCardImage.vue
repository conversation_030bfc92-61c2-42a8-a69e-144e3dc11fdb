<template>
  <div class="image-wrapper">
    <div class="image-card" ref="divRef">
      <NoteCardImageDrawing :key="item.value"
                            :item="item"
                            :edit-enabled="drawingEnabled"
                            @update-item="handlePenUpdate"
                            @update-state="disableDrawing"
      />
      <div v-if="cardEdit" class="image-actions">
        <el-button text type="primary" @click="toggleDrawing">
          <i v-if="drawingEnabled" class="bi bi-pencil-fill" style="font-size:20px"></i>
          <i v-else class="bi bi-pencil" style="font-size:20px"></i>
        </el-button>
        <el-upload :auto-upload="false"
                   :on-change="handleUpdate"
                   :show-file-list="false"
                   accept="image/*"
        >
          <button aria-disabled="false"
                  class="el-button is-link"
                  style="margin-right: 20px;"
                  type="button"
          >
            <el-text type="primary">重选</el-text>
          </button>
        </el-upload>
        <el-popconfirm cancelButtonText="取消"
                       confirmButtonText="删除"
                       title="确认删除 ?"
                       @confirm="handleDelete"
        >
          <template #reference>
            <el-text type="danger">删除</el-text>
          </template>
        </el-popconfirm>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref} from "vue";
import {useObjectUrl} from "@vueuse/core";
import NoteCardImageDrawing from "@/components/NotebookPanel/NoteCardCompt/NoteCardImageDrawing.vue";

const emits = defineEmits(['update-item', 'delete-item']);
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  cardEdit: {
    type: Boolean,
    required: true,
  },
});

const drawingEnabled = ref(false);

function disableDrawing() {
  drawingEnabled.value = false;
}

function toggleDrawing() {
  drawingEnabled.value = !drawingEnabled.value;
}

function handleDelete() {
  emits('delete-item', props.item);
}

function handlePenUpdate(item) {
  emits('update-item', item);
}

function handleUpdate(uploadFile) {
  const fileUrl = useObjectUrl(uploadFile.raw);
  emits('update-item', {...props.item, value: fileUrl, lines: []});
}

</script>
<style scoped>
.image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.image-card {
  width: 100%;
  padding-top: 0;
  padding-bottom: 0;
  background-color: var(--el-color-info-light-8);
  border-radius: 7px;
}

.image-actions {
  display: flex;
  align-items: center;
  justify-content: end;
  padding: 8px 10px 8px 0;
}
</style>