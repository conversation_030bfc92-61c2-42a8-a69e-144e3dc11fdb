<template>
  <van-nav-bar
    :title="title"
    :left-text="leftText"
    :left-arrow="showBack"
    :left-disabled="!showBack"
    :border="false"
    :safe-area-inset-top="true"
    @click-left="goBack"
  >
    <template #right>
      <el-text type="info"> 4.1018 {{ environment }}</el-text>
    </template>
  </van-nav-bar>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useRouter } from "vue-router";

const props = defineProps({
  showBack: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  emitBack: {
    type: Boolean,
    default: false,
  },
});

const router = useRouter();

const emits = defineEmits(["back"]);

// @ts-expect-error missing types
const environment = import.meta.env.MODE === "production" ? "" : "测试";

const leftText = computed(() => {
  return props.showBack ? "返回" : "";
});

const goBack = () => {
  if (props.showBack && props.emitBack) {
    emits("back");
  } else if (props.showBack) {
    router.back();
  }
};
</script>
