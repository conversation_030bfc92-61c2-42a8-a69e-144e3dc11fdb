<template>
  <div class="header">
    <div v-if="showInput" style="width: 100%;">
      <el-input ref="inputRef"
                v-model="title"
                type="textarea"
                @blur="onTitleChanged"
      />
    </div>
    <div v-else
         class="header-title"
    >
      <div @click="changeShowInput" style="flex-grow: 1; display: inline-flex">
        <div style="padding-right: 10px;">
          <el-tag type="info">{{ index }}</el-tag>
        </div>
        <el-text size="large" class="text">{{ props.title || "" }}</el-text>
      </div>
      <el-button v-if="!cardEdit" size="small"  style="margin-left: auto; z-index: 100" @click="handleToggleEdit">编辑</el-button>
      <el-button v-else size="small" style="margin-left: auto" @click="handleToggleEdit">X</el-button>
    </div>
  </div>
</template>

<script setup>
import {nextTick, ref, watch} from 'vue';

const props = defineProps({
  index: {
    type: Number,
    default: 1,
  },
  title: {
    type: String,
    required: true,
  },
  cardEdit: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['title-changed', 'toggle-edit']);

const title = ref(props.title);
const inputRef = ref(null);
const showInput = ref(false)

watch(() => props.title, () => title.value = props.title);

function changeShowInput() {
  showInput.value = true;
  nextTick(() => {
    inputRef.value.focus();
  })
}

function onTitleChanged() {
  emits('title-changed', title.value);
  showInput.value = false
}

function handleToggleEdit() {
  emits("toggle-edit");
}
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  padding: 8px;
}

.header-title {
  width: 100%;
  display: flex;
  justify-content: start;
  align-items: center;
}

.header-title .text {
  color:white;
  white-space: pre-wrap;
}
</style>