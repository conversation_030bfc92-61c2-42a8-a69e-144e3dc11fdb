<template>
  <el-container
    v-loading="loading"
    v-element-visibility="onElementVisibility"
    class="panel-container"
  >
    <el-main class="notes-container">
      <el-scrollbar ref="scrollbar">
        <div style="padding-bottom: 20px">
          <van-cell-group
            inset
            v-for="item in showingRects"
            :key="item.uid"
            class="cell-group"
          >
            <template #title>
              <div class="custom-title">文章：{{ item.name }}</div>
            </template>
            <div v-for="note in item.noteList" :key="note.uid" class="box-card">
              <FileNoteCard :rect="note.rect" :note="note" />
            </div>
          </van-cell-group>
        </div>
      </el-scrollbar>
    </el-main>
  </el-container>
</template>

<script setup>
import { ref, watch } from "vue";
import { vElementVisibility } from "@vueuse/components";
import { useAppStore } from "@/stores/app.js";
import {
  getAllNotes,
  getNoteFromRect,
  getRectByUID,
} from "@/lib/RectDatabase.js";
import { getFileMeta } from "@/lib/FileList.js";
import FileNoteCard from "@/components/NotebookPanel/FileNoteCard.vue";

const loading = ref(false);
const isVisible = ref(false);
const appStore = useAppStore();
const showingRects = ref([]);

watch(
  () => [appStore.currentSearchingRect, isVisible.value],
  () => {
    if (isVisible.value && !loading.value) {
      searchNotes();
    }
  },
  { immediate: true }
);

function onElementVisibility(state) {
  isVisible.value = state;
}

async function searchNotes() {
  if (!appStore.currentSearchingRect) return;
  loading.value = true;
  showingRects.value = [];
  const currentNote = await getNoteFromRect(appStore.currentSearchingRect.uid);
  if (!currentNote) {
    loading.value = false;
    return;
  }

  const allNotes = await getAllNotes();
  const likeNotes = allNotes
    .filter((note) => note.uid !== currentNote.uid)
    .filter((note) => compareNote(note, currentNote));
  if (!likeNotes || !likeNotes.length) {
    loading.value = false;
    return;
  }

  const files = [];
  const rects = [];
  for (const note of likeNotes) {
    if (!files.find((f) => f.id === note.fileId)) {
      const file = await getFileMeta(note.fileId);
      if (file) {
        files.push(file);
      }
    }

    if (!rects.find((r) => r.uid === note.uid)) {
      const rect = await getRectByUID(note.uid);
      if (rect) {
        rects.push(rect);
      }
    }
  }

  const fileMap = {};
  for (const file of files) {
    if (!fileMap[file.id]) {
      fileMap[file.id] = {
        uid: file.id,
        name: file.name,
        noteList: [],
      };
    }
    fileMap[file.id].noteList = likeNotes
      .filter((note) => note.fileId === file.id)
      .map((note) => {
        return {
          ...note,
          rect: rects.find((r) => r.uid === note.uid),
        };
      })
      .filter((note) => note.rect);
  }

  // 对fileMap中noteList按uid排序
  for (const key in fileMap) {
    fileMap[key].noteList.sort((a, b) => a.uid - b.uid);
  }

  // 将map转换为数组 [{name: 'xxx', noteList: []}]
  const list = [];
  for (const key in fileMap) {
    list.push(fileMap[key]);
  }

  showingRects.value = list;
  loading.value = false;
}

function compareNote(aNote, bNote) {
  const isSameTitle =
    aNote.noteTitle?.trim() &&
    bNote.noteTitle?.trim() &&
    aNote.noteTitle?.trim()?.includes(bNote.noteTitle?.trim());
  const isSameKeyword =
    aNote.keyword?.trim() &&
    bNote.keyword?.trim() &&
    aNote.keyword?.trim()?.includes(bNote.keyword?.trim());

  return isSameTitle || isSameKeyword;
}
</script>

<style scoped>
.panel-container {
  background-color: white;
  width: 100%;
  height: calc(100% - 20px);
}

.notes-container {
  padding: 0;
  margin-bottom: 10px;
}

.custom-title {
  font-size: 16px;
  color: #409eff;
}

.box-card {
  margin: 0;
  padding: 0 10px;
  background-color: #fdfaf3;
}
</style>
