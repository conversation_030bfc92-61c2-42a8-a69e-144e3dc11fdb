<template>
  <el-container v-loading="loading" class="panel-container">
    <el-main class="notes-container">
      <el-scrollbar ref="scrollbarRef">
        <div v-if="showingNotes.length === 0">
          <div style="text-align: center">
            <img src="@/assets/image/cactus.png" />
          </div>
        </div>
        <el-row>
          <el-col
            v-for="item in showingNotes"
            :key="item.uid"
            :xs="24"
            :sm="12"
            :lg="8"
          >
            <div class="box-card">
              <FileNoteCard :rect="item.rect" :note="item" />
            </div>
          </el-col>
        </el-row>
      </el-scrollbar>
    </el-main>
  </el-container>
</template>

<script setup>
import { ref, watch } from "vue";
import { getAllNotes, getRectByUID } from "@/lib/RectDatabase.js";
import { getFileMeta } from "@/lib/FileList.js";
import FileNoteCard from "@/components/NotebookPanel/FileNoteCard.vue";

const props = defineProps({
  searchKeyword: {
    type: String,
    default: "",
  },
  searchColorTagId: {
    type: Number,
    default: -1,
  },
});

const loading = ref(false);
const scrollbarRef = ref(null);
const showingNotes = ref([]);

watch(
  [() => props.searchKeyword, () => props.searchColorTagId],
  () => loadNotes(),
  { immediate: true }
);

async function loadNotes() {
  if (loading.value) {
    return;
  }

  loading.value = true;
  showingNotes.value = [];

  const allNotes = await getAllNotes();
  const likeNotes = allNotes.filter((note) => {
    if (props.searchColorTagId !== -1) {
      return (
        note.colorTagUid === props.searchColorTagId &&
        compareNote(note, props.searchKeyword)
      );
    } else {
      return compareNote(note, props.searchKeyword);
    }
  });

  if (!likeNotes || !likeNotes.length) {
    loading.value = false;
    return;
  }

  // 排序：最新的放在前面
  likeNotes.sort((a, b) => Number(b.uid) - Number(a.uid));

  const fileMap = {};
  const rects = [];
  for (const n of likeNotes) {
    let file = fileMap[n.fileId];
    if (!file) {
      file = await getFileMeta(n.fileId);
      if (file) {
        fileMap[n.fileId] = file;
      }
    }

    if (!rects.find((r) => r.uid === n.uid)) {
      const rect = await getRectByUID(n.uid);
      if (rect) {
        rects.push(rect);
      }
    }
  }

  const list = likeNotes
    .map((n) => ({
      ...n,
      fileName: fileMap[n.fileId]?.name,
      rect: rects.find((r) => r.uid === n.uid),
    }))
    .filter((n) => n.fileName && n.rect);

  showingNotes.value = list;
  loading.value = false;
  scrollbarRef.value?.setScrollTop(0);
}

function compareNote(aNote, keyword) {
  if (!keyword || !keyword.trim()) {
    return true;
  }

  const isSameTitle =
    aNote.noteTitle?.trim() &&
    aNote.noteTitle?.trim()?.includes(keyword.trim());
  const isSameKeyword =
    aNote.keyword?.trim() && aNote.keyword?.trim()?.includes(keyword.trim());

  return isSameTitle || isSameKeyword;
}
</script>

<style lang="scss" scoped>
.panel-container {
  background-color: white;
  width: 100%;
  height: 100%;
}

.box-card {
  margin: 5px;
  padding: 0 10px;
  background-color: #fdfaf3;
}
</style>
