<template>
  <div class="header">
    <div
      :contenteditable="!readonly"
      @focus="onInputFocus"
      @input="onTextInput"
      @blur="onInputBlur"
      :style="textAreaStyle"
      v-html="originTextSpan"
    ></div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";

defineProps({
  originText: {
    type: String,
    required: true,
    default: "",
  },
  originTextSpan: {
    type: String,
    required: true,
    default: "",
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["text-changed"]);
const isEditing = ref(false);

const textAreaStyle = computed(() => {
  return {
    backgroundColor: isEditing.value ? "white" : "transparent",
  };
});

function onInputFocus() {
  isEditing.value = true;
}

function onTextInput() {}

function onInputBlur(e) {
  isEditing.value = false;
  emits("text-changed", e.target.innerText, e.target.innerHTML);
}
</script>

<style scoped>
.header {
  padding-top: 5px;
  padding-bottom: 5px;
}
</style>
