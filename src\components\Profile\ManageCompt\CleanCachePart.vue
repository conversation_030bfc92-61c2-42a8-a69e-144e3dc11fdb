<template>
  <div class="parent" @click="cleanCache">
    <el-text>清理缓存</el-text>
    <div class="content">
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElLoading, ElMessage } from "element-plus";
import { UAParser } from "ua-parser-js";

function cleanCache() {
  const loading = ElLoading.service({
    fullscreen: true,
    text: "清理缓存中...",
  });
  const parser = new UAParser();
  const result = parser.getResult();
  if (result.os.name === "iOS") {
    loading.close();
    ElMessage.error("iOS 系统暂不支持清理缓存");
    return;
  } else if (result.os.name === "Android") {
    if (typeof window.ReactNativeWebView !== "undefined") {
      const data = {
        action: "webview-clean-cache",
      };
      window.ReactNativeWebView?.postMessage(JSON.stringify(data));
    } else {
      window.location.reload();
    }
  } else {
    window.location.reload();
  }
}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
