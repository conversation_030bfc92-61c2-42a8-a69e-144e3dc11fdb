<template>
  <div
    class="dialog"
    ref="dialogRef"
    @mousedown="dragStart"
    @mousemove="drag"
    @mouseup="dragEnd"
    @touchstart="dragStart"
    @touchmove="drag"
    @touchend="dragEnd"
  >
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { useResizeObserver } from "@vueuse/core";

const props = defineProps({
  dialogLocation: {
    type: String,
  },
  draggable: {
    type: Boolean,
    default: true,
  },
});
const dialogRef = ref(null);

// 检查鼠标触发元素是否是可拖动部分 防止拖动了笔记内部的滚动条带着对话框也移动位置
const isPartOfScrollComponent = (targetElement) => {
  let classListArray = Array.from(targetElement.classList);
  if (
    classListArray.some(
      (className) =>
        className.startsWith("el-tabs") || className.startsWith("dialog")
    )
  ) {
    return false;
  }
  return true;
};

//拖动移动
let dragging = ref(false);
let dragStartX = ref(0);
let dragStartY = ref(0);
const dragStart = (event) => {
  if (!props.draggable) {
    return;
  }
  if (isPartOfScrollComponent(event.target)) {
    return;
  }
  dragging.value = true;
  if (event.type === "touchstart") {
    dragStartX.value = event.touches[0].clientX - dialogRef.value.offsetLeft;
    dragStartY.value = event.touches[0].clientY - dialogRef.value.offsetTop;
  } else {
    dragStartX.value = event.clientX - dialogRef.value.offsetLeft;
    dragStartY.value = event.clientY - dialogRef.value.offsetTop;
  }
};

const drag = (event) => {
  if (dragging.value) {
    let clientX, clientY;
    if (event.type === "touchmove") {
      clientX = event.touches[0].clientX;
      clientY = event.touches[0].clientY;
    } else {
      clientX = event.clientX;
      clientY = event.clientY;
    }
    let newLeft = clientX - dragStartX.value;
    let newTop = clientY - dragStartY.value;
    dialogRef.value.style.left = `${newLeft}px`;
    dialogRef.value.style.top = `${newTop}px`;
  }
};

const dragEnd = () => {
  dragging.value = false;
};

// 监听dialogLocation的变化
watch(
  () => props.dialogLocation,
  (newLocation) => {
    if (newLocation === "left") {
      moveToScreenLeft();
    }
    if (newLocation === "center") {
      moveToCenter();
    }
    if (newLocation === "bottom") {
      moveToScreenBottom();
    }
  }
);

function moveToScreenLeft() {
  if (window.innerWidth < 576) {
    // 小于576px的屏幕不做处理
  } else if (window.innerWidth >= 576 && window.innerWidth <= 768) {
    dialogRef.value.style.left = "30%";
    dialogRef.value.style.transform = "translateX(-50%, -50%)";
  } else if (window.innerWidth > 768 && window.innerWidth <= 992) {
    dialogRef.value.style.left = "30%";
    dialogRef.value.style.transform = "translateX(-50%, -50%)";
  } else {
    dialogRef.value.style.left = "35%";
    dialogRef.value.style.transform = "translateX(-50%, -50%)";
  }
}

const moveToCenter = () => {
  dialogRef.value.style.left = "50%";
  dialogRef.value.style.top = "50%";
  dialogRef.value.style.transform = "translate(-50%, -50%)";
};

const moveToScreenBottom = () => {
  const screenHeight = window.innerHeight; // 获取屏幕的高度
  dialogRef.value.style.top = `${screenHeight - 200}px`; // 设置dialog的顶部距离屏幕底部100px
  dialogRef.value.style.bottom = "auto"; // 清除之前可能设置的bottom值
  dialogRef.value.style.left = "50%"; // 水平居中
  dialogRef.value.style.transform = "translateX(-50%)"; // 调整水平位置
};

// 横竖屏切换重置位置
useResizeObserver(document.body, () => {
  if (props.dialogLocation === "left") {
    moveToScreenLeft();
  } else if (props.dialogLocation === "center") {
    moveToCenter();
  } else if (props.dialogLocation === "bottom") {
    moveToScreenBottom();
  } else {
    moveToCenter();
  }
});
</script>

<style scoped>
.dialog {
  position: fixed;
  /* 定位相对于浏览器窗口 */
  top: 50%;
  /* 从顶部开始的位置为窗口高度的50% */
  left: 50%;
  /* 从左边开始的位置为窗口宽度的50% */
  transform: translate(-50%, -50%);
  /* 使用 transform 平移，将对话框的中心点设置为其自身宽高的50% */
  background-color: white;
  padding: 10px 15px;
}
</style>
