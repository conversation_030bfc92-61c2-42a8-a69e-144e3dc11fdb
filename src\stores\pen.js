import {reactive} from "vue";
import {defineStore} from "pinia";
import {DefaultColors} from "@/components/PenFloatToolbox/DefaultValues";

const defaultPenAttrs = [
    {
        type: "PEN",
        color: "rgb(0, 0, 0)",
        panelColors: [...DefaultColors.PEN],
        activeWidthIndex: 0,
        lineWidths: [15, 30, 75]
    },
    {
        type: "LINE",
        color: "rgb(0, 0, 0)",
        panelColors: [...DefaultColors.LINE],
        activeWidthIndex: 0,
        lineWidths: [15, 30, 75]
    },
    {
        type: "DOTTED_LINE",
        color: "rgb(0, 0, 0)",
        panelColors: [...DefaultColors.DASH],
        activeWidthIndex: 0,
        lineWidths: [15, 30, 75]
    },
    {
        type: "HIGHLIGHT_PEN",
        color: "rgb(254, 208, 48)",
        panelColors: [...DefaultColors.HIGHLIGHTER],
        activeWidthIndex: 0,
        lineWidths: [35, 70, 100]
    },
    {
        type: "ERASER",
        color: "rgb(0, 0, 0)",
        panelColors: [],
        activeWidthIndex: 0,
        lineWidths: [35, 70, 100]
    }
]

export const usePenStore = defineStore("pen", () => {
    const pens = reactive(defaultPenAttrs)

    function getPen(type) {
        return pens.find(pen => pen.type === type)
    }

    return {
        pens,
        getPen
    }
})