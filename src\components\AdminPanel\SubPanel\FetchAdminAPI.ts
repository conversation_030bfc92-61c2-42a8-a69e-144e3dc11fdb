type RequestData = {
  [key: string]: string | number | boolean | string[];
};

/**
 * 通用GET请求函数
 */
async function fetchGetRequest(url: string) {
  return fetch(
    `${url}${url.includes("?") ? "&" : "?"}t=${new Date().getTime()}`,
    {
      headers: {
        ...getJWTHeaders(),
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
      cache: "no-store",
    }
  ).then((response) => response.json());
}

export async function fetchUsers(page: number) {
  return fetchGetRequest(`/api/admin/users?page=${page}`);
}

export async function fetchMembers(page: number) {
  return fetchGetRequest(`/api/admin/members?page=${page}`);
}

export async function fetchMemberDetails(id: string) {
  return fetchGetRequest(`/api/admin/members/${id}`);
}

export async function fetchLevels(page: number) {
  return fetchGetRequest(`/api/admin/levels?page=${page}`);
}

export async function fetchLevelDetails(id: string) {
  return fetchGetRequest(`/api/admin/levels/${id}`);
}

export async function fetchDeleteLevel(id: string) {
  return fetch(`/api/admin/levels/${id}`, {
    method: "DELETE",
    headers: { ...getJWTHeaders() },
  }).then((response) => response.json());
}

export async function fetchAddLevel(data: RequestData) {
  return fetch(`/api/admin/levels`, {
    method: "POST",
    headers: { ...getJWTHeaders(), "Content-Type": "application/json" },
    body: JSON.stringify(data),
  }).then((response) => response.json());
}

export async function fetchUpdateLevel(id: string, data: RequestData) {
  return fetch(`/api/admin/levels/${id}`, {
    method: "PUT",
    headers: { ...getJWTHeaders(), "Content-Type": "application/json" },
    body: JSON.stringify(data),
  }).then((response) => response.json());
}

export async function fetchPromotions(page: number) {
  return fetchGetRequest(`/api/admin/promotions?page=${page}`);
}

export async function fetchPromotionDetails(id: string) {
  return fetchGetRequest(`/api/admin/promotions/${id}`);
}

export async function fetchDeletePromotion(id: string) {
  return fetch(`/api/admin/promotions/${id}`, {
    method: "DELETE",
    headers: { ...getJWTHeaders() },
  }).then((response) => response.json());
}

export async function fetchAddPromotionFormData() {
  return fetchGetRequest(`/api/admin/promotions/form`);
}

export async function fetchAddPromotion(data: RequestData) {
  return fetch(`/api/admin/promotions`, {
    method: "POST",
    headers: { ...getJWTHeaders(), "Content-Type": "application/json" },
    body: JSON.stringify(data),
  }).then((response) => response.json());
}

export async function fetchUpdatePromotion(id: string, data: RequestData) {
  return fetch(`/api/admin/promotions/${id}`, {
    method: "PUT",
    headers: { ...getJWTHeaders(), "Content-Type": "application/json" },
    body: JSON.stringify(data),
  }).then((response) => response.json());
}

export function getJWTHeaders() {
  const token = localStorage.getItem("jwt_token");
  return {
    Authorization: `JWT ${token}`,
  };
}
