<template>
  <div v-if="showColorSelect">
    <h4 style="margin-top: 15px">选择颜色：</h4>
    <div style="display: flex; flex-wrap: wrap">
      <div
          v-for="colorStr in defaultColors"
          :key="colorStr"
          class="color-button-circle"
      >
        <div
            v-if="colorStr !== colorStrLocal"
            :style="{ color: colorStr }"
            @click="changeColor(colorStr)"
        >
          <i class="bi bi-circle-fill"></i>
        </div>
        <div v-else :style="{ color: colorStr }">
          <i class="bi bi-check-circle-fill"></i>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watchEffect, onMounted } from "vue";
import { DefaultColors } from "@/components/PenFloatToolbox/DefaultValues";
const defaultColors = [...DefaultColors.NORMAL];
const props = defineProps({
  aliveColor: {
    type: String,
    default: "",
  },
  currentTool: {
    type: Object,
    default: () => ({}),
  },
});

const colorStrLocal = ref("");
const showColorSelect = ref(false);
watchEffect(() => {
  colorStrLocal.value = props.aliveColor;
});
onMounted(() => {
  if (
      props.currentTool.type === "PEN" ||
      props.currentTool.type === "LINE" ||
      props.currentTool.type === "DOTTED_LINE" ||
      props.currentTool.type === "HIGHLIGHT_PEN" ||
      props.currentTool.type === "HIGHLIGHT_PEN_STRAIGHT"
  ) {
    showColorSelect.value = true;
  }
});
const emit = defineEmits(["color-changed"]);
const changeColor = (newColor) => {
  colorStrLocal.value = newColor;
  emit("color-changed", newColor);
};
</script>

<style scoped>
.color-button-circle {
  padding: 10px;
  font-size: 1.5rem;
}
</style>
