<template>
  <div v-loading="animationLoading" class="swipe-image-container">
    <UseElementVisibility v-slot="{ isVisible }">
      <div v-show="componentLoading" class="scan-loading"></div>
      <div v-if="isVisible" class="remove-button" @click="onRemove">
        <el-icon><Close /></el-icon>
      </div>
      <img :src="imageSrc" alt="" />
    </UseElementVisibility>
  </div>
</template>

<script setup>
import { useObjectUrl } from "@vueuse/core";
import { UseElementVisibility } from "@vueuse/components";

const props = defineProps({
  image: {
    type: File,
    required: true,
  },
  componentLoading: {
    type: Boolean,
    default: false,
  },
  animationLoading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["remove"]);
const imageSrc = useObjectUrl(props.image);

function onRemove() {
  emit("remove");
}
</script>

<style scoped>
img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.swipe-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swipe-image-container > div {
  height: 100%;
  position: relative;
}

.scan-loading {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  background: transparent;
}

.scan-loading::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  width: 100%;
  height: 100%;
  background-size: 20px 20px;
  background-image: linear-gradient(to right, #409eff 1px, transparent 1px),
    linear-gradient(to bottom, #409eff 1px, transparent 1px);
  animation: scan-animation 1s ease-in-out infinite;
}

@keyframes scan-animation {
  0% {
    height: 20px;
  }
  100% {
    height: calc(100% - 20px);
  }
}

.scan-loading::after {
  content: "";
  position: absolute;
  inset: 20px;
  width: calc(100% - 40px);
  height: 8px;
  background: linear-gradient(to right, transparent, #409eff, transparent);
  filter: drop-shadow(0 0 20px #409eff) drop-shadow(0 0 60px #409eff);
  animation: animateLine 1s ease-in-out infinite;
}

@keyframes animateLine {
  0% {
    top: 20px;
  }
  100% {
    top: calc(100% - 20px);
  }
}

.remove-button {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.remove-button i {
  color: white;
  font-size: 20px;
}
</style>
