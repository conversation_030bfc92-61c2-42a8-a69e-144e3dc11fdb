<template>
  <div class="parent" @click="gotoLoginMethod">
    <el-text>登录方式管理</el-text>
    <div class="content">
      <el-text type="info">管理绑定</el-text>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from "vue-router";

const router = useRouter();
function gotoLoginMethod() {
  router.push({ path: "/profile/login-method" });
}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}
</style>
