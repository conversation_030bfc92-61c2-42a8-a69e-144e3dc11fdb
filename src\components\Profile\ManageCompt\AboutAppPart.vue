<template>
  <div class="parent" @click="showAbout">
    <el-text>关于拍照精读</el-text>
    <div class="content">
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </div>
  <el-dialog
    v-model="showDialog"
    title="关于拍照精读"
    width="90%"
    :append-to-body="true"
  >
    <el-text>
      <h3>拍照精读</h3>
      <p>开发者：杭州求智科技有限公司</p>
      <p>联系方式：<EMAIL></p>
      <p>
        拍照精读软件是一款英语精读、批注、笔记、归类复习的工具软件，支持纸质材料拍照识别、划词笔记、
        划词翻译、手写批注，通过"相关笔记"功能，实现不同阅读材料间知识点的即刻回溯，通过"标签"、"自定分类"
        实现 知识整理归类，英语积累、学习事半功倍。
      </p>
    </el-text>
    <el-carousel
      type="card"
      height="75vh"
      :interval="10000"
      indicator-position="outside"
      style="margin-top: 2rem"
    >
      <el-carousel-item v-for="index in 4" :key="index">
        <div class="carousel-item">
          <img :src="getImageUrl(index)" alt="About image" />
        </div>
      </el-carousel-item>
    </el-carousel>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ArrowRight } from "@element-plus/icons-vue";

const showDialog = ref(false);

function showAbout() {
  showDialog.value = true;
}

function getImageUrl(index: number) {
  // This will work if using Vite
  return new URL(`../Assets/About/${index}.png`, import.meta.url).href;
}
</script>

<style scoped>
div.parent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

div.content {
  display: flex;
  align-items: center;
}

div.content > * {
  margin-left: 5px;
}

.carousel-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.carousel-item img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style>
