<template>
  <div v-loading="loading">
    <div style="margin-bottom: 15px; display: flex; justify-content: end">
      <el-button type="primary" @click="add">添加</el-button>
      <el-button type="primary" @click="reload">刷新</el-button>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column type="selection" width="50" />
      <el-table-column
        prop="name"
        label="名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="discountType" label="折扣类型" />
      <el-table-column prop="discountValue" label="折扣大小" />
      <el-table-column prop="maxDiscountValue" label="最大折扣金额" />
      <el-table-column prop="newCustomerOnly" label="限定新用户">
        <template #default="{ row }">
          <el-tag :type="row.newCustomerOnly ? 'success' : 'danger'">
            {{ row.newCustomerOnly ? "是" : "否" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="onlyFirstOrder" label="限定新订单">
        <template #default="{ row }">
          <el-tag :type="row.onlyFirstOrder ? 'success' : 'danger'">
            {{ row.onlyFirstOrder ? "是" : "否" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="description"
        label="描述"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="startDate" label="开始时间" />
      <el-table-column prop="endDate" label="结束时间" />
      <el-table-column prop="levelCount" label="关联套餐数量" />
      <el-table-column fixed="right" label="操作" min-width="120">
        <template #default="{ row }">
          <el-button link type="primary" size="small" @click="edit(row.id)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      style="
        margin-top: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <el-pagination
        v-model:current-page="currentPage"
        background
        layout="prev, pager, next"
        :page-count="totalPage"
      />
    </div>
    <el-dialog
      v-model="showDialog"
      title="详情"
      width="90%"
      top="50px"
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <PromotionDetail
        :id="currentEditId"
        @update="reload"
        @delete="onDelete"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { fetchPromotions } from "@/components/AdminPanel/SubPanel/FetchAdminAPI";
import PromotionDetail from "./PromotionDetail.vue";

type LevelData = {
  name: string;
  discountType: string;
  discountValue: number;
  maxDiscountValue: number;
  newCustomerOnly: boolean;
  onlyFirstOrder: boolean;
  description: string;
  startDate: string;
  endDate: string;
  applicableLevels: never[];
};

const loading = ref(true);
const totalPage = ref(1);
const currentPage = ref(1);
const showDialog = ref(false);
const currentEditId = ref("");
const tableDataRaw = ref<LevelData[]>([]);
const tableData = computed(() => {
  return tableDataRaw.value.map((item) => {
    return {
      ...item,
      discountType: item.discountType === "PERCENTAGE" ? "百分比" : "固定金额",
      startDate: dayjs(item.startDate).format("YYYY-MM-DD"),
      endDate: dayjs(item.endDate).format("YYYY-MM-DD"),
      levelCount: item.applicableLevels.length,
    };
  });
});

onMounted(() => {
  fetchData(1);
});

watch(currentPage, (newPage) => {
  fetchData(newPage);
});

function reload() {
  fetchData(currentPage.value);
}

function add() {
  currentEditId.value = "";
  showDialog.value = true;
}

function edit(id: string) {
  currentEditId.value = id;
  showDialog.value = true;
}

function onDelete() {
  showDialog.value = false;
  reload();
}

function fetchData(page: number) {
  loading.value = true;
  fetchPromotions(page)
    .then((data) => {
      if (data.resCode && data.resCode === "SUCCESS") {
        tableDataRaw.value = data.data?.promotions ?? [];
        totalPage.value = data.pagination?.totalPages ?? 1;
      } else if (data.message) {
        ElMessage.error(data.message);
      }
    })
    .catch(() => {
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
