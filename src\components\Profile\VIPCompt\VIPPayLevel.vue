<template>
  <div v-loading="loading">
    <div style="overflow-x: scroll; scrollbar-width: none">
      <el-space>
        <el-card
          v-for="level in levelList"
          :key="level.id"
          shadow="never"
          class="level-card"
          :class="{ selected: selectedLevel?.id === level.id }"
          @click="selectLevel(level)"
        >
          <div>
            <h4 class="level-title">{{ level.name }}</h4>
            <p
              :style="{ visibility: level.discountType ? 'visible' : 'hidden' }"
            >
              立减
              {{ level.discountType === "AMOUNT" ? "￥" : "" }}
              {{ level.discountValue }}
              {{ level.discountType === "PERCENTAGE" ? "%" : "" }}
            </p>
          </div>
          <div style="margin-top: 1rem">
            <p
              :style="{
                visibility:
                  level.originalPrice && level.originalPrice !== level.price
                    ? 'visible'
                    : 'hidden',
              }"
            >
              <el-text type="info">
                <del>￥{{ level.originalPrice }}</del>
              </el-text>
            </p>
            <h4 class="level-price">
              ￥<span class="price-number">{{ level.price }}</span>
            </h4>
          </div>
        </el-card>
      </el-space>
    </div>
    <div style="margin-top: 2rem">
      <el-button
        type="warning"
        size="large"
        class="pay-btn"
        :disabled="!selectedLevel"
        @click="sendRequest(selectedLevel!)"
      >
        <h3>确认协议并开通</h3>
      </el-button>
    </div>
    <!-- 会员协议 -->
    <div class="agree-checkbox">
      <el-checkbox v-model="agreed" style="margin-right: 5px" />
      <div style="display: flex; align-items: center">
        我已阅读并同意
        <el-link
          type="primary"
          :underline="false"
          style="margin: 0 5px"
          @click="showAgreement"
        >
          《会员协议》
        </el-link>
      </div>
    </div>
    <el-dialog
      v-model="showAgreementDialog"
      title="会员协议"
      top="50px"
      width="90%"
      :append-to-body="true"
      :show-close="true"
    >
      <MemberAgreementText />
    </el-dialog>
    <el-dialog
      v-model="showQrCodeDialog"
      title="二维码付款"
      top="50px"
      width="90%"
      :append-to-body="true"
      :show-close="true"
    >
      <div style="text-align: center">
        <img :src="qrcode" alt="QR Code" />
      </div>
      <div style="text-align: center">
        <el-button type="success" @click="confirmPay">已完成支付</el-button>
        <el-button type="info" @click="confirmPay">已取消支付</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-model="showConfirmDialog"
      title="支付确认"
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <el-card shadow="never">
        <div style="text-align: center;">
          <el-button
            type="success"
            @click="confirmPay"
            style="margin-bottom: 15px"
            >已完成支付</el-button
          >
          <el-button type="info" @click="confirmPay" style="margin-bottom: 15px"
            >已取消支付</el-button
          >
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { UAParser } from "ua-parser-js";
import { useQRCode } from "@vueuse/integrations/useQRCode";
// @ts-expect-error import js lib
import { useAppStore } from "@/stores/app";
import { useProfileStore } from "@/stores/profile";
import MemberAgreementText from "@/components/Profile/DocumentCompt/MemberAgreementText.vue";
import {
  getVipLevels,
  createOrder,
} from "@/components/Profile/Requests/FetchVipApi";

declare global {
  interface Window {
    ReactNativeWebView?: {
      postMessage: (message: string) => void;
    };
  }
}

type VipLevel = {
  id: string;
  name: string;
  price: string;
  promotionId?: string;
  originalPrice?: string;
  discountType?: "" | "AMOUNT" | "PERCENTAGE";
  discountValue?: string;
};

const emits = defineEmits(["refresh-pay", "show-login"]);

const loading = ref(false);
const appStore = useAppStore();
const profileStore = useProfileStore();
const agreed = ref(false);
const showAgreementDialog = ref(false);
const showConfirmDialog = ref(false);
const showQrCodeDialog = ref(false);
const levelList = ref<VipLevel[]>([]);
const selectedLevel = ref<VipLevel>();
const currentOrderId = ref<string>("");
const currentQrCodeUrl = ref<string>("");
const qrcode = useQRCode(currentQrCodeUrl, {
  width: window.innerWidth * 0.4,
  height: window.innerHeight * 0.4,
});
const needShowLogin = computed(() => !profileStore.userProfile);

onMounted(() => {
  loading.value = true;

  // parse user-agent
  const parser = new UAParser();
  const result = parser.getResult();
  if (result.device.type === "mobile") {
    appStore.deviceType = "mobile";
  } else if (result.device.type === "tablet") {
    appStore.deviceType = "tablet";
  } else {
    appStore.deviceType = "web";
  }

  getVipLevels()
    .then((res) => {
      if (res.resCode && res.resCode === "SUCCESS") {
        levelList.value = res.data.levels;
      } else {
        ElMessage.error("获取失败");
      }
    })
    .catch((err) => {
      ElMessage.error(err.message ?? "获取失败");
    })
    .finally(() => {
      if (levelList.value.length > 0) {
        selectedLevel.value = levelList.value[0];
      }
      loading.value = false;
    });
});

// 用户直接关闭窗口时也检查
watch(
  () => showConfirmDialog.value,
  (newValue) => {
    if (!newValue) {
      emits("refresh-pay", currentOrderId.value);
    }
  }
);

watch(
  () => showQrCodeDialog.value,
  (newValue) => {
    if (!newValue) {
      emits("refresh-pay", currentOrderId.value);
    }
  }
);

function selectLevel(level: VipLevel) {
  selectedLevel.value = level;
}

const showAgreement = () => {
  showAgreementDialog.value = true;
};

function sendRequest(level: VipLevel) {
  if (!agreed.value) {
    ElMessage.error("请先阅读并同意会员协议");
    return;
  }

  if (needShowLogin.value) {
    emits("show-login");
    ElMessage.error("请先登录");
    return;
  }

  loading.value = true;
  createOrder(level.id, level.promotionId, appStore.deviceType)
    .then((res) => {
      if (res.resCode && res.resCode === "SUCCESS") {
        if (res?.data?.form) {
          currentOrderId.value = res.data.orderId;
          if (typeof window.ReactNativeWebView !== "undefined") {
            const data = {
              action: "open-pay-url",
              url: res.data.form,
            };
            window.ReactNativeWebView?.postMessage(JSON.stringify(data));
          } else {
            window.open(res.data.form, "_blank");
          }
          showConfirmDialog.value = true;
        } else if (res?.data?.qrCode) {
          currentOrderId.value = res.data.orderId;
          currentQrCodeUrl.value = res.data.qrCode;
          showQrCodeDialog.value = true;
        } else {
          ElMessage.error("创建订单失败");
        }
      } else {
        ElMessage.error("创建订单失败");
      }
    })
    .catch((err) => {
      ElMessage.error(err.message ?? "创建订单失败");
    })
    .finally(() => {
      loading.value = false;
    });
}

function confirmPay() {
  showConfirmDialog.value = false;
  showQrCodeDialog.value = false;
}
</script>
<style lang="scss" scoped>
.level-card {
  min-width: 150px;
  max-width: 200px;
  border-radius: 10px;
  transition: none;

  &.selected {
    border-radius: 15px 20px;
    border: 3px solid var(--el-color-warning);

    :deep(.el-card__body) {
      padding: 8px;
    }
  }

  :deep(.el-card__body) {
    padding: 10px;
  }

  .level-title {
    font-size: 16px;
    font-weight: bold;
  }

  .level-price {
    font-weight: bold;

    .price-number {
      font-size: 24px;
    }
  }
}

.pay-btn {
  width: 100%;
  border-radius: 20px;

  h3 {
    margin: 0;
    font-weight: bold;
  }

  &:not(.is-disabled) {
    h3 {
      color: black;
    }
  }
}

.agree-checkbox {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
