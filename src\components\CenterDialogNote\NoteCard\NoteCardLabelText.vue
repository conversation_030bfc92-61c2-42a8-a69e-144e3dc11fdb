<template>
  <div class="header header-title">
    <InputTextArea
      :text="text"
      :readonly="readonly"
      :rows="rows"
      :transparent="true"
      @text-changed="onTitleChanged"
    />
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import InputTextArea from "@/components/BaseComponents/InputTextArea.vue";

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  rows: {
    type: Number,
    default: 2,
  },
});

const emits = defineEmits(["topValue-changed", "toggle-edit"]);

const name = ref(props.name);
const text = ref(props.text);

watch(
  () => props.name,
  () => (name.value = props.name)
);
watch(
  () => props.text,
  () => (text.value = props.text)
);

function onTitleChanged(text) {
  emits("topValue-changed", name.value, text);
}
</script>

<style scoped>
.header {
  padding-top: 5px;
  padding-bottom: 5px;
}

.header-title {
  width: 100%;
  display: flex;
  justify-content: start;
  align-items: center;
}

.header-title .text {
  white-space: pre-wrap;
}

.gray-text input {
  color: gray;
}

.auto-height-textarea {
  padding-left: 5px;
  padding-right: 5px;
  overflow: hidden;
  width: 100%;
  border: none;
  /* background-color: #F0F2F5; */
  background-color: white;
  border-radius: 5px;
}
</style>
