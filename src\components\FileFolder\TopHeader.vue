<template>
  <van-nav-bar title="所有笔记">
    <template #left>
      <!-- <van-icon name="search" size="18" /> -->
    </template>
    <template #right>
      <!-- <van-popover
        v-model:show="showPopover"
        :actions="actions"
        @select="onSelect"
        placement="bottom-end"
      >
        <template #reference>
          <van-icon
            name="back-top"
            color="#E6A23C"
            size="1.3rem"
            badge="9"
            @click="CloudClick"
          />
        </template>
      </van-popover> -->
    </template>
  </van-nav-bar>
</template>

<script setup>
import { ref } from "vue";
import { showToast } from "vant";

function CloudClick() {
  console.log("CloudClick 云同步和设置");
}

const actions = [
  { text: "备份数据到百度网盘" },
  { text: "同步数据到华为云盘" },
  { text: "选项三" },
];
const showPopover = ref(false);
const onSelect = (action) => showToast(action.text);
</script>

<style scoped>
.child {
  width: 40px;
  height: 40px;
  background: #f2f3f5;
  border-radius: 4px;
}
</style>
