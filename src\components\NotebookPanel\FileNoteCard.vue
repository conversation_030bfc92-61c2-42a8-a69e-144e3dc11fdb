<template>
  <el-tabs v-model="activeName">
    <el-tab-pane label="笔记" name="RECT_Note" :lazy="true">
      <div v-if="currentRect" class="note-container">
        <NoteCardOuter
          :key="currentRect.uid"
          :rect="currentRect"
          :card-edit="false"
        />
      </div>
    </el-tab-pane>
    <el-tab-pane label="翻译" name="RECT_RESEARCH" :lazy="true">
      <TransFrame
        v-if="translateKeyWord"
        :keyWord="translateKeyWord"
        :fullscreen="true"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
// @ts-expect-error import js lib
import NoteCardOuter from "@/components/CenterDialogNote/NoteCardOuterDlg.vue";
// @ts-expect-error import js lib
import TransFrame from "@/components/CenterDialogNote/Trans/TransFrame.vue";

const props = defineProps({
  rect: {
    type: Object,
    required: true,
  },
  note: {
    type: Object,
    required: true,
  },
});

const activeName = ref("RECT_Note");
const currentRect = ref();
const translateKeyWord = ref("");

onMounted(() => {
  currentRect.value = props.rect;
  translateKeyWord.value = props.note.noteTitle;
});
</script>

<style lang="scss" scoped></style>
