<template>
  <el-card v-loading="loading" style="background-color: #67c23a">
    <div class="vip-banner">
      <div class="vip-banner-left">
        <div class="vip-banner-left-title">VIP会员</div>
        <div class="vip-banner-left-desc">会员到期时间：{{ endDate }}</div>
      </div>
      <div class="vip-banner-right">
        <el-button round @click="handleClick"> 立即续费 </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { getVipInfo } from "@/components/Profile/Requests/FetchVipApi";

const router = useRouter();
const loading = ref(false);
const endDate = ref("");

onMounted(() => {
  fetchVipInfo();
});

function handleClick() {
  router.push("/profile/member-info");
}

function fetchVipInfo() {
  loading.value = true;
  getVipInfo()
    .then((res) => {
      if (res.resCode && res.resCode === "SUCCESS") {
        endDate.value = dayjs(res.data.endDate).format("YYYY-MM-DD");
      } else if (res.message) {
        ElMessage.error(res.message);
      } else {
        ElMessage.error("获取用户账户信息失败");
      }
    })
    .catch((err) => {
      ElMessage.error(err.message ?? "获取用户账户信息失败");
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<style lang="scss" scoped>
.vip-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .vip-banner-left {
    .vip-banner-left-title {
      font-size: 20px;
      font-weight: bold;
      color: white;
    }
    .vip-banner-left-desc {
      font-size: 14px;
      color: white;
    }
  }
  .vip-banner-right {
    .el-button {
      width: 100px;
    }
  }
}
</style>
