<template>
  <div>
    <iframe id="bing" ref="bingIframe"
            :src="searchUrl"
            :style="{height: iframeHeight + 'px'}"
            style="box-sizing: border-box; width: 100%; border: none"
    ></iframe>
  </div>

</template>

<script setup>
import {ref, watch} from 'vue'
import {useResizeObserver} from "@vueuse/core";

const props = defineProps({
  keyWord: String
})

const bingIframe = ref(null)
let iframeHeight = ref(window.innerHeight - 105) // 新增一个 ref 来存储窗口的高度
let searchUrl = ref(`https://m.so.com/s?q=${props.keyWord}`)
watch(
    () => props.keyWord,
    () => {
      searchUrl.value = `https://m.so.com/s?q=${props.keyWord}`
    }
)

useResizeObserver(document.body, () => {
  iframeHeight.value = window.innerHeight - 105
})
</script>