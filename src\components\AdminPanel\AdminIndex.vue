<template>
  <el-tabs tab-position="left" type="card">
    <el-tab-pane label="开始" lazy>
      <div class="tab-content">
        <h1>欢迎使用后台管理系统</h1>
        <p>请在左侧选择需要管理的内容</p>
      </div>
    </el-tab-pane>
    <el-tab-pane label="用户" lazy>
      <div class="tab-content">
        <AdminUser />
      </div>
    </el-tab-pane>
    <el-tab-pane label="会员" lazy>
      <div class="tab-content">
        <AdminMember />
      </div>
    </el-tab-pane>
    <el-tab-pane label="套餐" lazy>
      <div class="tab-content">
        <AdminLevel />
      </div>
    </el-tab-pane>
    <el-tab-pane label="优惠" lazy>
      <div class="tab-content">
        <AdminPromotion />
      </div>
    </el-tab-pane>
  </el-tabs>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { useProfileStore } from "@/stores/profile";
import AdminUser from "./SubPanel/AdminUser/AdminUser.vue";
import AdminMember from "./SubPanel/AdminMember/AdminMember.vue";
import AdminLevel from "./SubPanel/AdminLevel/AdminLevel.vue";
import AdminPromotion from "./SubPanel/AdminPromotion/AdminPromotion.vue";

const router = useRouter();
const profileStore = useProfileStore();

onMounted(() => {
  if (!profileStore.isAdmin) {
    router.push("/");
  }
});
</script>

<style scoped>
.tab-content {
  padding: 20px;
}
</style>
