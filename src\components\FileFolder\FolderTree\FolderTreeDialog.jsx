import {ref} from "vue";
import {showDialog} from "vant";
import FolderTree from "@/components/FileFolder/FolderTree/FolderTree.vue";

export async function showFolderTreeDialog() {
    const timestamp = Date.now().toString();
    const nodes = ref([]);

    function updateNodes(newNodes) {
        nodes.value = newNodes;
    }

    return showDialog({
        title: "移动到",
        message: () => (
            <FolderTree key={timestamp} onUpdateNodes={updateNodes}></FolderTree>
        ),
        width: 480,
        messageAlign: "left",
        showConfirmButton: true,
        showCancelButton: true,
    })
        .then(() => {
            return Promise.resolve(nodes.value);
        })
}